/**
 * Owl Carousel v2.3.4
 */
.owl-theme .owl-nav {
  margin-top: 68px;
  text-align: center;
  -webkit-tap-highlight-color: transparent;
  display: none; 
}
  .owl-theme .owl-nav [class*='owl-'] {
    color: #FFF;
    font-size: 14px;
    margin: 80px;
    padding: 4px 7px;
    display: inline-block;
    cursor: pointer;
    border-radius: 3px; 
  
  }
    .owl-theme .owl-nav [class*='owl-']:hover {
      color: #FFF;
      text-decoration: none; }
  .owl-theme .owl-nav .disabled {
    opacity: 1;
    cursor: default; }

.owl-theme .owl-nav.disabled + .owl-dots {
  margin-top: 10px; }

.owl-theme .owl-dots {
  text-align: center;
  background: 0 0;
  color: inherit;
  border: none;
  padding: 0!important;
  font: inherit;
  outline: 0;
  cursor: pointer;
  margin-top: 14px;
  position: relative; }
  .owl-theme .owl-dots .owl-dot {
    display: inline-block;
    zoom: 1;
    *display: inline; }
    .owl-theme .owl-dots .owl-dot span {
      width: 44px;
      height: 8px;
      margin: -48px 7px;
      display: block;
      -webkit-backface-visibility: visible;
      transition: opacity 200ms ease;
      background: #202020; }
    .owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots span {
      background: #4b9bc8;
      width: 70px;
      height: 8px;
      display: block;
      -webkit-backface-visibility: visible;
      transition: opacity 200ms ease; }
      @media only screen and (max-width: 1024px) {
        .owl-carousel .owl-item img {
           height: 500px;
        }
	}

    @media only screen and (max-width: 480px) {
      .owl-theme .owl-nav [class*='owl-'] {
        margin: -59px;
        }
	  }
    @media only screen and (max-width: 768px) {
      .owl-theme .owl-nav [class*='owl-'] {
        margin: -59px;
        }
	  }