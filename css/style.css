@charset "UTF-8";
@import url("https://fonts.googleapis.com/css2?family=Barlow&display=swap");
.h-yazi-ortalama {
  text-align: center; }
  @media only screen and (max-width: 40em) {
    .h-yazi-ortalama {
      text-align: center;
      margin-left: 0;
      padding: 3rem; } }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-ortalama {
      text-align: center;
      margin-left: 0;
      padding: 3rem; } }

.h-yazi-solda {
  text-align: left; }

.h-yazi-ozel {
  text-align: center;
  margin-left: -120px; }

.h-yazi-margin-kucuk {
  margin-bottom: 1.5rem !important; }

.h-yazi-margin-kucuk {
  margin-bottom: -1rem; }

.buton-ortalama-blog {
  margin-top: 20rem;
  text-align: center; }

.buton-ortalama-team {
  margin-top: 13rem;
  text-align: center; }

.h-yazi-margin-orta {
  margin-bottom: 4rem !important; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-orta {
      margin-bottom: 3rem !important; } }

.h-yazi-margin-buyuk {
  margin-bottom: 3rem !important; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-buyuk {
      margin-bottom: -5rem !important; } }

.h-yazi-margin-buyuk-2 {
  margin-bottom: 12rem !important; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-buyuk-2 {
      margin-bottom: -5rem !important; } }

.h-yazi-margin-buyuk-3 {
  margin-top: -2.5rem; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-buyuk-3 {
      margin-top: -2.5rem; } }

.h-yazi-margin-buyuk-4 {
  margin-bottom: 12rem; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-buyuk-4 {
      margin-bottom: 3rem; } }

.h-yazi-margin-orta-2 {
  margin-bottom: -5rem !important; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-orta-2 {
      margin-bottom: 8rem !important; } }

.h-yazi-margin-orta-3 {
  margin-bottom: 1rem !important;
  text-align: center; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-orta-3 {
      margin-bottom: 3rem !important;
      text-align: center; } }

.h-yazi-margin-orta-4 {
  margin-bottom: 1rem !important; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-orta-4 {
      margin-bottom: -3rem !important; } }

.h-yazi-margin-5 {
  margin-top: 10rem; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-5 {
      margin-top: -20rem; } }

.h-yazi-margin-50 {
  margin-top: 0rem; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-50 {
      margin-top: 1rem; } }

.h-yazi-margin-4 {
  margin-top: 2rem;
  text-align: center; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-4 {
      margin-top: 1rem;
      text-align: center; } }

.h-yazi-margin-altta {
  margin-top: 40.5rem; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-altta {
      margin-top: 50rem; } }

.h-yazi-margin-ozel {
  margin-bottom: 4rem !important; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-ozel {
      margin-bottom: 3rem !important; } }

.h-yazi-margin-ustte-buyuk {
  margin-top: 8rem; }

.h-yazi-margin-ustte-enbuyuk {
  margin-top: 10rem; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-ustte-enbuyuk {
      margin-top: 1rem; } }

.h-yazi-margin-kucuk-1 {
  margin-top: -1rem; }
  @media only screen and (max-width: 812px) {
    .h-yazi-margin-kucuk-1 {
      margin-left: 11rem; } }
  @media only screen and (max-width: 375px) {
    .h-yazi-margin-kucuk-1 {
      margin-left: 16rem; } }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-kucuk-1 {
      margin-top: 1rem;
      text-align: center; } }

.h-yazi-margin-kucuk-21 {
  margin-top: -1rem; }
  @media only screen and (max-width: 375px) {
    .h-yazi-margin-kucuk-21 {
      /*margin-left: 16rem;*/ } }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-kucuk-21 {
      margin-top: 1rem;
      text-align: center;
      width: 100%;
      display: flex;
      justify-content: center;
      flex-wrap: wrap; } }

.h-yazi-margin-5 {
  margin-top: 5rem;
  text-align: center; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-5 {
      margin-top: -15rem;
      text-align: center; } }

.bosluk-kaldir {
  margin-top: -2rem; }
  @media only screen and (max-width: 56.25em) {
    .bosluk-kaldir {
      margin-top: -2rem; } }

.bosluk-kaldir2 {
  margin-top: -4rem; }
  @media only screen and (max-width: 56.25em) {
    .bosluk-kaldir2 {
      margin-top: -2rem; } }

.h-yazi-margin-st {
  margin-top: 5rem;
  text-align: center; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-st {
      margin-top: 5rem;
      text-align: center; } }

.h-yazi-margin-55 {
  margin-top: -1rem;
  text-align: center;
  padding: 35px; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-55 {
      margin-top: -15rem;
      text-align: center; } }

.h-yazi-margin-6 {
  margin-top: 5rem;
  margin-bottom: 5rem; }
  @media only screen and (max-width: 56.25em) {
    .h-yazi-margin-6 {
      margin-top: -2rem; } }

a {
  text-decoration: none;
  color: #fff; }

.container {
  padding: 0rem;
  margin: 0 auto; }

@media only screen and (max-width: 56.25em) {
  .bosluk {
    margin-top: -40rem; } }
@media only screen and (max-width: 40em) {
  .bosluk {
    margin-top: -40rem; } }

.bosluk1 {
  margin-top: 13rem; }
  @media only screen and (max-width: 56.25em) {
    .bosluk1 {
      margin-top: 10rem; } }
  @media only screen and (max-width: 40em) {
    .bosluk1 {
      margin-top: 10rem; } }

*,
*::after,
*::before {
  margin: 0;
  padding: 0;
  box-sizing: inherit; }

html {
  font-size: 62.5%;
  scroll-behavior: smooth; }
  @media only screen and (max-width: 75em) {
    html {
      font-size: 56.25%; } }
  @media only screen and (max-width: 56.25em) {
    html {
      font-size: 50%; } }
  @media only screen and (max-width: 40em) {
    html {
      font-size: 30%; } }
  @media only screen and (min-width: 112.5em) {
    html {
      font-size: 75%; } }

body {
  box-sizing: border-box;
  padding: 3rem;
  background: #fff; }
  @media only screen and (max-width: 56.25em) {
    body {
      padding: 0; } }

::selection {
  background-color: #373075;
  color: #fff; }

::-webkit-scrollbar-thumb {
  background: #373075; }

::-webkit-scrollbar {
  background: #fff;
  width: .8rem;
  height: .8rem; }

#pointer-dot {
  left: 0;
  top: 0;
  margin-top: -.5rem;
  margin-left: -.3rem;
  width: 0;
  height: 0;
  border: 2.5px solid #b29a7b;
  position: fixed;
  border-radius: .4rem;
  pointer-events: none;
  transition: border-color 0.5s;
  z-index: 9999; }

#pointer-ring {
  left: 0;
  top: 0;
  width: 0;
  height: 0;
  padding: 1.5rem;
  border: .2rem solid #f1f1f1;
  position: fixed;
  border-radius: 10rem;
  pointer-events: none;
  z-index: 9999; }

.hakkimizda-bolumu-anasayfa {
  padding: 0rem 0 5rem;
  background-color: #fff; }
  @media only screen and (max-width: 56.25em) {
    .hakkimizda-bolumu-anasayfa {
      padding: 99rem 0 1rem;
      margin-top: -50rem; } }
  @media only screen and (max-width: 56.25em) {
    .hakkimizda-bolumu-anasayfa {
      padding: 0rem 0 44rem;
      margin-top: -24rem; } }

.hakkimizda-bolumu-hakkimizda {
  padding: 5rem 0 1rem;
  background-color: #fff; }
  @media only screen and (max-width: 56.25em) {
    .hakkimizda-bolumu-hakkimizda {
      padding: 0rem 0;
      margin-top: -55rem; } }
  @media only screen and (max-width: 56.25em) {
    .hakkimizda-bolumu-hakkimizda {
      padding: 0rem 0;
      margin-top: -75rem; } }

.count-bolumu {
  padding: 0rem 0 0rem;
  background-color: #141414; }
  @media only screen and (max-width: 56.25em) {
    .count-bolumu {
      padding: 99rem 0 1rem;
      margin-top: -50rem; } }
  @media only screen and (max-width: 56.25em) {
    .count-bolumu {
      padding: 0rem 0 1rem;
      margin-top: -55rem; } }

.hata-404 {
  padding: 5rem 0 5rem;
  background-color: #fff; }
  @media only screen and (max-width: 56.25em) {
    .hata-404 {
      padding: 0rem 0;
      margin-top: 20vh; } }
  @media only screen and (max-width: 56.25em) {
    .hata-404 {
      padding: 0rem 0;
      margin-top: -20vh; } }

.hizmetler-alani {
  padding: 6rem 0 5rem;
  background-image: linear-gradient(to right bottom, #fff, #fbfbfb); }
  @media only screen and (max-width: 56.25em) {
    .hizmetler-alani {
      padding: 10rem 0 17rem;
      margin-top: -20rem; } }

.hizmetler-aciklama {
  padding: 6rem 0 5rem;
  background-image: linear-gradient(to right bottom, #fff, #fbfbfb); }
  @media only screen and (max-width: 56.25em) {
    .hizmetler-aciklama {
      padding: 10rem 0 17rem; } }

.services1 {
  padding: 5rem 0 5rem;
  background-color: #fff;
  background: url("../img/service-term.jpg"); }
  @media only screen and (max-width: 56.25em) {
    .services1 {
      padding: 10rem 0; } }

.services2 {
  padding: 5rem 0 5rem;
  background-color: #e72121; }
  @media only screen and (max-width: 56.25em) {
    .services2 {
      padding: 10rem 0; } }

.services3 {
  padding: 0rem 0 5rem;
  background-color: #ffffff; }
  @media only screen and (max-width: 56.25em) {
    .services3 {
      padding: 10rem 0; } }

.services4 {
  padding: 5rem 0 5rem;
  background-color: #ffffff; }
  @media only screen and (max-width: 56.25em) {
    .services4 {
      padding: 10rem 0; } }

.yorumlar-alani {
  padding: 5rem 0 5rem;
  background-color: #fff; }
  @media only screen and (max-width: 56.25em) {
    .yorumlar-alani {
      padding: 10rem 0; } }

.post-alani-sayfa {
  padding: 5rem 0 7rem;
  background: #fff; }
  @media only screen and (max-width: 56.25em) {
    .post-alani-sayfa {
      padding: 10rem 0; } }

.paketler-alani {
  padding: 5rem 0 5rem;
  background-color: #fff; }
  @media only screen and (max-width: 56.25em) {
    .paketler-alani {
      padding: 21rem 0; } }

.yorumlar-alani-sayfa {
  padding: 5rem 0 8rem;
  background: #f7f7f7; }
  @media only screen and (max-width: 56.25em) {
    .yorumlar-alani-sayfa {
      padding: 10rem 0; } }

.ekip-alani-sayfa {
  padding: 0rem 0 1rem; }
  @media only screen and (max-width: 56.25em) {
    .ekip-alani-sayfa {
      padding: 10rem 0; } }

.news-alani-sayfa {
  padding: 5rem 0 9rem;
  background: #fff; }
  @media only screen and (max-width: 56.25em) {
    .news-alani-sayfa {
      padding: 10rem 0; } }

@media only screen and (max-width: 40em) {
  img.haber-gorsel {
    width: 250px;
    text-align: center; } }
@media only screen and (max-width: 56.25em) {
  img.haber-gorsel {
    text-align: center; } }

.yorumlar-alani {
  position: relative;
  padding: 5rem 0 5rem;
  background-image: linear-gradient(to right bottom, #ffffff, #fbfbfb);
  background-size: cover; }
  @media only screen and (max-width: 56.25em) {
    .yorumlar-alani {
      padding: 5rem 0 0rem; } }

.form-alani {
  padding: 40rem 0;
  background-image: linear-gradient(to right bottom, #33c4e7, #0c52aa);
  margin-top: -30vh; }

.projeler {
  position: relative;
  padding: 5rem 0 5rem;
  background-image: linear-gradient(to right bottom, #ffffff, #fbfbfb);
  background-size: cover; }
  @media only screen and (max-width: 56.25em) {
    .projeler {
      padding: 15rem 0 16rem; } }

.projeler-sayfa-alani {
  padding: 5rem 0 5rem;
  background-color: #fff;
  /*margin-top: -10rem;*/ }
  @media only screen and (max-width: 56.25em) {
    .projeler-sayfa-alani {
      padding: 15rem 0 16rem; } }

.markalar {
  padding: 3rem 0 2rem;
  background: #2e276a;
  background-size: cover;
  display: inline-block;
  width: 100%; }
  @media only screen and (max-width: 56.25em) {
    .markalar {
      padding: 15rem 0 16rem; } }

.markalarw {
  padding: 3rem 0 2rem;
  background: #fff;
  background-size: cover;
  display: inline-block;
  width: 100%; }
  @media only screen and (max-width: 56.25em) {
    .markalarw {
      padding: 15rem 0 16rem; } }

.bottom {
  padding: 5rem 0 1rem;
  background-image: linear-gradient(to right bottom, #fffefe, #ffffff);
  background-size: cover;
  display: inline-block;
  width: 100%; }
  @media only screen and (max-width: 56.25em) {
    .bottom {
      padding: 15rem 0 16rem; } }

.hizmetler-detay-sayfasi-alani {
  padding: 20rem 0 5rem;
  margin-top: -20vh;
  background-color: #fff; }
  @media only screen and (max-width: 56.25em) {
    .hizmetler-detay-sayfasi-alani {
      padding: 0rem 0rem 20rem;
      margin-top: 20vh; } }
  @media only screen and (max-width: 56.25em) {
    .hizmetler-detay-sayfasi-alani {
      padding: 0rem 0rem 20rem;
      margin-top: -20vh; } }

.proje-detay-sayfasi-alani {
  padding: 5rem 0 1rem;
  /*margin-top:-20vh;*/
  background-color: #fff; }
  @media only screen and (max-width: 56.25em) {
    .proje-detay-sayfasi-alani {
      padding: 40rem 0rem 5rem;
      margin-top: 20vh; } }
  @media only screen and (max-width: 56.25em) {
    .proje-detay-sayfasi-alani {
      padding: 40rem 0rem 5rem;
      margin-top: -20vh; } }

.iletisim-icon-alani {
  padding: 20rem 20px 10rem;
  margin-top: -20vh;
  background-color: #fff; }
  @media only screen and (max-width: 56.25em) {
    .iletisim-icon-alani {
      padding: 15rem 0;
      margin-top: 20vh; } }
  @media only screen and (max-width: 56.25em) {
    .iletisim-icon-alani {
      padding: 15rem 0;
      margin-top: -20vh; } }

.iletisim-form-alani {
  padding: 20rem 20px 10rem;
  margin-top: -20vh;
  background-color: #fff; }
  @media only screen and (max-width: 56.25em) {
    .iletisim-form-alani {
      padding: 0rem 0;
      margin-top: 20vh; } }
  @media only screen and (max-width: 56.25em) {
    .iletisim-form-alani {
      padding: 0rem 0;
      margin-top: -20vh; } }

span.date {
  font-size: 1.4rem;
  font-weight: 700;
  color: #141414;
  text-align: center; }
  @media only screen and (max-width: 40em) {
    span.date {
      font-size: 2.8rem; } }
  @media (min-width: 600px) and (max-width: 900px) {
    span.date {
      font-size: 3.5rem; } }
  @media (min-width: 900px) and (max-width: 1200px) {
    span.date {
      font-size: 2rem; } }

span.category {
  font-size: 1.4rem;
  font-weight: 700;
  color: #141414;
  text-align: center; }
  @media only screen and (max-width: 40em) {
    span.category {
      font-size: 2.8rem; } }
  @media (min-width: 600px) and (max-width: 900px) {
    span.category {
      font-size: 3.5rem; } }
  @media (min-width: 900px) and (max-width: 1200px) {
    span.category {
      font-size: 2rem; } }

span.tt {
  font-size: 1.4rem;
  font-weight: 700;
  color: #141414;
  text-align: center; }
  @media only screen and (max-width: 40em) {
    span.tt {
      font-size: 2.8rem; } }
  @media (min-width: 600px) and (max-width: 900px) {
    span.tt {
      font-size: 3.5rem; } }
  @media (min-width: 900px) and (max-width: 1200px) {
    span.tt {
      font-size: 2rem; } }

.datesection {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap; }

/* Efektler */
@keyframes solahareket {
  0% {
    opacity: 0;
    transform: translateX(-10rem); }
  80% {
    transform: translateX(1rem); }
  100% {
    opacity: 1;
    transform: translate(0); } }
@keyframes sagahareket {
  0% {
    opacity: 0;
    transform: translateX(10rem); }
  80% {
    transform: translateX(-1rem); }
  100% {
    opacity: 1;
    transform: translate(0); } }
@keyframes butonefekt {
  0% {
    opacity: 0;
    transform: translateY(3rem); }
  100% {
    opacity: 1;
    transform: translate(0); } }
.jssorl-009-spin img {
  animation-name: jssorl-009-spin;
  animation-duration: 1.6s;
  animation-iteration-count: infinite;
  animation-timing-function: linear; }

@keyframes jssorl-009-spin {
  from {
    transform: rotate(0deg); }
  to {
    transform: rotate(360deg); } }
/*jssor slider bullet skin 132 css*/
.jssorb132 {
  position: absolute; }

.jssorb132 .i {
  position: absolute;
  cursor: pointer; }

.jssorb132 .i .b {
  fill: #fff;
  fill-opacity: 0.8;
  stroke: #DE6FAA;
  stroke-width: 1600;
  stroke-miterlimit: 10;
  stroke-opacity: 0.7; }

.jssorb132 .i:hover .b {
  fill: #DE6FAA;
  fill-opacity: .7;
  stroke: #fff;
  stroke-width: 2000;
  stroke-opacity: 0.8; }

.jssorb132 .iav .b {
  fill: #DE6FAA;
  stroke: #fff;
  stroke-width: 2400;
  fill-opacity: 0.8;
  stroke-opacity: 1; }

.jssorb132 .i.idn {
  opacity: 0.3; }

.jssora051 {
  display: block;
  position: absolute;
  cursor: pointer; }

.jssora051 .a {
  fill: none;
  stroke: #fff;
  stroke-width: 360;
  stroke-miterlimit: 10; }

.jssora051:hover {
  opacity: .8; }

.jssora051.jssora051dn {
  opacity: .5; }

.jssora051.jssora051ds {
  opacity: .3;
  pointer-events: none; }

/* 
Animasyonlar
*/
body {
  font-family: "Poppins", sans-serif;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 1.7;
  color: #797979; }

.header__slider-alani {
  position: absolute;
  top: 40%;
  left: 40%;
  transform: translate(-30%, -30%);
  text-align: center; }

.golge {
  font-size: 9.2rem;
  color: #212529;
  line-height: 42px;
  font-weight: 900;
  letter-spacing: -.05em;
  -webkit-text-fill-color: #FFF;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #2a2a2a;
  white-space: nowrap;
  opacity: .1 !important;
  text-align: center; }
  @media only screen and (max-width: 40em) {
    .golge {
      margin-top: 86rem; } }
  @media only screen and (max-width: 375px) {
    .golge {
      margin-top: 102rem; } }
  @media (min-width: 645px) and (max-width: 764px) {
    .golge {
      margin-top: 70rem; } }
  @media (min-width: 768px) and (max-width: 900px) {
    .golge {
      margin-top: 57rem; } }
  @media (min-width: 901px) and (max-width: 998px) {
    .golge {
      margin-top: 28rem; } }
  @media (min-width: 1000px) and (max-width: 1024px) {
    .golge {
      margin-top: 20rem; } }
  @media (min-width: 1025px) and (max-width: 1088px) {
    .golge {
      margin-top: -20rem; } }
  @media (min-width: 1090px) and (max-width: 1190px) {
    .golge {
      margin-top: -20rem; } }
  @media (min-width: 1191px) and (max-width: 1350px) {
    .golge {
      margin-top: -15rem; } }

.divider {
  opacity: .5;
  height: auto;
  max-width: 100%;
  vertical-align: top; }

.alan {
  margin-top: -80px; }
  @media only screen and (max-width: 1024px) {
    .alan {
      display: none; } }
  @media only screen and (max-width: 56.25em) {
    .alan {
      display: none; } }

.alanb {
  margin-top: -30px; }
  @media only screen and (max-width: 1024px) {
    .alanb {
      display: none; } }
  @media only screen and (max-width: 56.25em) {
    .alanb {
      display: none; } }

.slider-yazi-icerikleri {
  color: #fff;
  text-transform: uppercase;
  margin-bottom: 2rem;
  backface-visibility: hidden; }
  .slider-yazi-icerikleri--1 {
    display: block;
    font-size: 6rem;
    font-weight: 400;
    letter-spacing: 3rem;
    animation-name: solahareket;
    animation-duration: 1s;
    animation-timing-function: ease-in-out; }
    @media only screen and (max-width: 40em) {
      .slider-yazi-icerikleri--1 {
        letter-spacing: 1rem;
        font-size: 5rem; } }
  .slider-yazi-icerikleri--2 {
    display: block;
    font-size: 1.9rem;
    font-weight: 700;
    letter-spacing: 1rem;
    animation: sagahareket 1s ease-out; }
    @media only screen and (max-width: 40em) {
      .slider-yazi-icerikleri--2 {
        letter-spacing: .5rem; } }

.h2-baslik-anasayfa {
  font-size: 4rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  background-image: linear-gradient(to right, #33c4e7, #0c52aa);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .2rem;
  transition: all .2s;
  border-bottom: 0.1rem solid #33c4e7; }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-anasayfa {
      font-size: 6rem; } }
  @media only screen and (max-width: 40em) {
    .h2-baslik-anasayfa {
      font-size: 4.7rem; } }
  .h2-baslik-anasayfa:hover {
    transform: skewY(2deg) skewX(15deg) scale(1.1);
    /*text-shadow: .5rem 1rem 2rem rgba($siyah-renk,.2);*/ }

.h2-baslik-anasayfa-ozel {
  font-size: 2.5rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  background-image: linear-gradient(to right, #373075, #2e276a);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .1rem; }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-anasayfa-ozel {
      font-size: 5rem; } }

.h2-baslik-ahb {
  font-size: 2.5rem;
  font-weight: 900;
  text-transform: uppercase;
  background-image: linear-gradient(to right, #373075, #2e276a);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .1rem;
  text-align: center; }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-ahb {
      font-size: 5rem; } }

.h2-baslik-bottom {
  font-size: 1.6rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  background-image: linear-gradient(to right, #373075, #2e276a);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .1rem; }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-bottom {
      font-size: 3rem; } }

.h2-baslik-404 {
  font-size: 5rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  background-image: linear-gradient(to right, #373075, #2e276a);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .2rem;
  margin-bottom: 3rem; }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-404 {
      font-size: 7rem; } }

.h2-baslik-footer {
  font-size: 3rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  background-image: linear-gradient(to right, #fff, #fff);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .2rem;
  margin-bottom: 3rem; }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-footer {
      font-size: 7rem; } }

.h2-baslik-projeler-ozel {
  font-size: 3rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  background-image: linear-gradient(to right, #259cb9, #33c4e7);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .2rem;
  margin-bottom: 3rem; }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-projeler-ozel {
      font-size: 7rem; } }

.h2-baslik-iletisim-ozel {
  font-size: 4rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  background-image: linear-gradient(to right, #373075, #2e276a);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .2rem;
  margin-bottom: 3rem; }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-iletisim-ozel {
      font-size: 7rem;
      margin-top: -90px; } }

.h2-baslik-popup {
  font-size: 3rem;
  font-weight: 900;
  text-align: center;
  text-transform: uppercase;
  background-image: linear-gradient(to right, #373075, #3b3664);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .2rem;
  transition: all .2s;
  margin-bottom: 3rem;
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap; }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-popup {
      margin-top: .1rem;
      font-size: 5rem; } }

.paragraf-popup {
  font-size: 1.8rem;
  font-weight: 300;
  color: #797979;
  text-align: left; }
  .paragraf-popup:not(:last-child) {
    margin-bottom: 2rem;
    margin-top: -.8rem;
    width: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap; }
  @media only screen and (max-width: 56.25em) {
    .paragraf-popup {
      font-size: 2.1rem; } }

.baslik-3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #000;
  text-align: center; }
  @media only screen and (max-width: 56.25em) {
    .baslik-3 {
      font-size: 3.9rem;
      text-align: center;
      /*padding: 1rem;*/ } }
  @media (min-width: 641px) and (max-width: 820px) {
    .baslik-3 {
      margin-top: 10rem; } }

.baslik-3white {
  font-size: 1.6rem;
  font-weight: 700;
  color: #fff;
  text-align: center; }
  @media only screen and (max-width: 56.25em) {
    .baslik-3white {
      font-size: 3.9rem;
      text-align: center;
      /*padding: 1rem;*/ } }

.baslik-3whitec {
  font-size: 4rem;
  font-weight: 700;
  color: #fff;
  text-align: center; }
  @media only screen and (max-width: 56.25em) {
    .baslik-3whitec {
      font-size: 10rem;
      text-align: center;
      /*padding: 1rem;*/ } }

.baslik-sol {
  font-size: 1.8rem;
  font-weight: 700;
  color: #ffffff;
  text-align: center; }
  @media only screen and (max-width: 56.25em) {
    .baslik-sol {
      font-size: 3.9rem;
      text-align: center;
      /*padding: 1rem;*/ } }
  @media (min-width: 641px) and (max-width: 820px) {
    .baslik-sol {
      margin-top: 10rem; } }

.baslik-sol-beyaz {
  font-size: 1.8rem;
  font-weight: 700;
  color: #ffffff;
  text-align: left; }
  @media only screen and (max-width: 56.25em) {
    .baslik-sol-beyaz {
      font-size: 3.9rem;
      text-align: left;
      /*padding: 1rem;*/ } }

.baslik-3-1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #000;
  text-align: left; }
  @media only screen and (max-width: 56.25em) {
    .baslik-3-1 {
      font-size: 3.9rem;
      /*padding: 1rem;*/ } }

.baslik-3-h {
  font-size: 2.8rem;
  font-weight: 700;
  color: #373075;
  text-align: left; }
  @media only screen and (max-width: 56.25em) {
    .baslik-3-h {
      font-size: 3.9rem;
      /*padding: 1rem;*/ } }

.baslik-33 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #373075;
  text-align: center; }
  @media only screen and (max-width: 56.25em) {
    .baslik-33 {
      font-size: 3.9rem;
      /*padding: 1rem;*/ } }

.baslik-star {
  font-size: 3.8rem;
  font-weight: 700;
  color: #373075;
  text-align: center; }
  @media only screen and (max-width: 56.25em) {
    .baslik-star {
      font-size: 3.9rem;
      /*padding: 1rem;*/ } }

.baslik-3-2 {
  font-size: 1.8rem;
  font-weight: 700;
  text-align: center;
  color: #fff; }
  @media only screen and (max-width: 56.25em) {
    .baslik-3-2 {
      font-size: 3.9rem;
      text-align: center;
      /*padding: 1rem;*/ } }

h2.h3-baslik-anasayfa-beyaz {
  width: 100%;
  display: block;
  font-size: 50px;
  font-weight: 800;
  color: #fff;
  text-align: center; }

.paragraf {
  font-size: 1.4rem;
  font-weight: 400;
  color: #797979; }
  .paragraf a {
    color: #0c52aa; }
  .paragraf:not(:last-child) {
    margin-bottom: 1.4rem; }
  @media only screen and (max-width: 56.25em) {
    .paragraf {
      font-size: 3.4rem; } }

.paragraf-sol-beyaz {
  font-size: 1.4rem;
  font-weight: 400;
  color: #ffffff; }
  .paragraf-sol-beyaz a {
    color: #0c52aa; }
  .paragraf-sol-beyaz:not(:last-child) {
    margin-bottom: 1.4rem; }
  @media only screen and (max-width: 56.25em) {
    .paragraf-sol-beyaz {
      font-size: 3.4rem; } }

.paragraf-sol-beyaz-orta {
  font-size: 1.4rem;
  font-weight: 400;
  color: #ffffff;
  text-align: center; }
  .paragraf-sol-beyaz-orta a {
    color: #0c52aa; }
  .paragraf-sol-beyaz-orta:not(:last-child) {
    margin-bottom: 1.4rem; }
  @media only screen and (max-width: 56.25em) {
    .paragraf-sol-beyaz-orta {
      font-size: 3.4rem; } }

.paragraf-ahp {
  font-size: 1.4rem;
  font-weight: 400;
  color: #797979;
  text-align: center; }
  .paragraf-ahp a {
    color: #0c52aa; }
  .paragraf-ahp:not(:last-child) {
    margin-bottom: 1.4rem; }
  @media only screen and (max-width: 56.25em) {
    .paragraf-ahp {
      font-size: 3.4rem; } }

.paragraf-pdetay {
  font-size: 1.8rem;
  font-weight: 300;
  color: #797979; }
  .paragraf-pdetay a {
    color: #0c52aa; }
  .paragraf-pdetay:not(:last-child) {
    margin-bottom: 2rem; }
  @media only screen and (max-width: 56.25em) {
    .paragraf-pdetay {
      font-size: 3.2rem;
      margin-bottom: -20rem; } }

.paragraf-404 {
  font-size: 1.8rem;
  font-weight: 300;
  color: #797979;
  line-height: 4rem; }
  .paragraf-404 a {
    color: #373075; }
  .paragraf-404:not(:last-child) {
    margin-bottom: 2rem; }
  @media only screen and (max-width: 56.25em) {
    .paragraf-404 {
      font-size: 3.4rem; } }

.proje-detay-paragraf {
  font-size: 1.8rem;
  font-weight: 300;
  padding: 0rem 15rem 0rem 15rem; }
  .proje-detay-paragraf:not(:last-child) {
    margin-bottom: 2rem; }
  @media only screen and (max-width: 56.25em) {
    .proje-detay-paragraf {
      padding: 0rem 5rem 0rem 5rem;
      font-size: 3.4rem; } }

.h2-baslik-hizmetler {
  font-size: 2.7rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  background-image: linear-gradient(to right, #259cb9, #33c4e7);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .2rem;
  margin-bottom: 1rem; }
  .h2-baslik-hizmetler__paragraf {
    font-size: 2.4rem;
    font-weight: 300;
    color: #259cb9;
    text-align: left; }
    .h2-baslik-hizmetler__paragraf:not(:last-child) {
      margin-bottom: 5rem; }
      @media only screen and (max-width: 56.25em) {
        .h2-baslik-hizmetler__paragraf:not(:last-child) {
          margin-bottom: -9rem;
          text-align: center; } }
    @media only screen and (max-width: 56.25em) {
      .h2-baslik-hizmetler__paragraf {
        font-size: 3.5rem; } }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-hizmetler {
      font-size: 7rem; } }

.h2-baslik-hizmetler-4 {
  font-size: 2.6rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  background-image: linear-gradient(to right, #fff, #fff);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .1rem;
  text-align: center; }
  .h2-baslik-hizmetler-4__paragraf {
    font-size: 1.6rem;
    font-weight: 400;
    color: #fff;
    text-align: center;
    margin-bottom: 3rem;
    text-align: center; }
    .h2-baslik-hizmetler-4__paragraf:not(:last-child) {
      margin-bottom: 3rem; }
      @media only screen and (max-width: 56.25em) {
        .h2-baslik-hizmetler-4__paragraf:not(:last-child) {
          margin-bottom: -9rem; } }
    @media only screen and (max-width: 56.25em) {
      .h2-baslik-hizmetler-4__paragraf {
        font-size: 3.5rem;
        text-align: center;
        padding: 3rem; } }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-hizmetler-4 {
      font-size: 5rem; } }

.h2-baslik-hizmetler-2 {
  font-size: 2.6rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  background-image: linear-gradient(to right, #373075, #2e276a);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .1rem;
  text-align: center; }
  .h2-baslik-hizmetler-2__paragraf {
    font-size: 1.6rem;
    font-weight: 400;
    color: #797979;
    text-align: center;
    margin-bottom: 3rem;
    text-align: center; }
    .h2-baslik-hizmetler-2__paragraf:not(:last-child) {
      margin-bottom: -1rem; }
      @media only screen and (max-width: 56.25em) {
        .h2-baslik-hizmetler-2__paragraf:not(:last-child) {
          margin-bottom: -2rem; } }
    @media only screen and (max-width: 56.25em) {
      .h2-baslik-hizmetler-2__paragraf {
        font-size: 3.5rem;
        text-align: center;
        padding: 3rem; } }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-hizmetler-2 {
      font-size: 5rem; } }

.h2-baslik-hizmetler-21 {
  font-size: 2.6rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  color: #ffffff;
  letter-spacing: .1rem;
  text-align: center; }
  .h2-baslik-hizmetler-21__paragraf {
    font-size: 1.6rem;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    margin-bottom: 3rem;
    text-align: center; }
    .h2-baslik-hizmetler-21__paragraf:not(:last-child) {
      margin-bottom: -1rem; }
      @media only screen and (max-width: 56.25em) {
        .h2-baslik-hizmetler-21__paragraf:not(:last-child) {
          margin-bottom: -2rem; } }
    @media only screen and (max-width: 56.25em) {
      .h2-baslik-hizmetler-21__paragraf {
        font-size: 3.5rem;
        text-align: center;
        padding: 3rem; } }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-hizmetler-21 {
      font-size: 5rem; } }

.h2-baslik-hizmetler-5 {
  font-size: 2.6rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  background-image: linear-gradient(to right, #373075, #2e276a);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .1rem;
  text-align: center; }
  .h2-baslik-hizmetler-5__paragraf {
    font-size: 1.6rem;
    font-weight: 400;
    color: #797979;
    text-align: center;
    margin-bottom: 3rem;
    text-align: center; }
    .h2-baslik-hizmetler-5__paragraf:not(:last-child) {
      margin-bottom: 2rem; }
      @media only screen and (max-width: 56.25em) {
        .h2-baslik-hizmetler-5__paragraf:not(:last-child) {
          margin-bottom: -2rem; } }
    @media only screen and (max-width: 56.25em) {
      .h2-baslik-hizmetler-5__paragraf {
        font-size: 3.5rem;
        text-align: center;
        padding: 3rem; } }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-hizmetler-5 {
      font-size: 5rem; } }

.h2-baslik-hizmetler-3 {
  font-size: 2.6rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  background-image: linear-gradient(to right, #373075, #2e276a);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .1rem; }
  .h2-baslik-hizmetler-3__paragraf {
    font-size: 1.6rem;
    font-weight: 400;
    color: #797979;
    text-align: center;
    margin-bottom: 1rem; }
    @media only screen and (max-width: 56.25em) {
      .h2-baslik-hizmetler-3__paragraf:not(:last-child) {
        margin-bottom: -5rem;
        text-align: center;
        align-items: center;
        clear: both;
        margin: auto; } }
    @media only screen and (max-width: 56.25em) {
      .h2-baslik-hizmetler-3__paragraf {
        font-size: 3.5rem;
        text-align: left;
        padding: 3rem; } }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-hizmetler-3 {
      font-size: 5rem; } }

ul.hizmetdetay {
  padding-left: 2rem;
  line-height: 4rem;
  font-size: 1.5rem;
  font-weight: 400;
  color: #797979; }
  @media only screen and (max-width: 56.25em) {
    ul.hizmetdetay {
      padding-left: 2rem;
      line-height: 8rem;
      font-size: 3rem; } }
  @media only screen and (max-width: 40em) {
    ul.hizmetdetay {
      padding-left: 2rem;
      line-height: 8rem;
      font-size: 3rem; } }

summary {
  font-size: 1.7rem;
  font-weight: 600;
  background-color: #fafafa;
  background-image: radial-gradient(circle at 40% 91%, rgba(251, 251, 251, 0.04) 0%, rgba(251, 251, 251, 0.04) 50%, rgba(229, 229, 229, 0.04) 50%, rgba(229, 229, 229, 0.04) 100%), radial-gradient(circle at 66% 97%, rgba(36, 36, 36, 0.04) 0%, rgba(36, 36, 36, 0.04) 50%, rgba(46, 46, 46, 0.04) 50%, rgba(46, 46, 46, 0.04) 100%), radial-gradient(circle at 86% 7%, rgba(40, 40, 40, 0.04) 0%, rgba(40, 40, 40, 0.04) 50%, rgba(200, 200, 200, 0.04) 50%, rgba(200, 200, 200, 0.04) 100%), radial-gradient(circle at 15% 16%, rgba(99, 99, 99, 0.04) 0%, rgba(99, 99, 99, 0.04) 50%, rgba(45, 45, 45, 0.04) 50%, rgba(45, 45, 45, 0.04) 100%), radial-gradient(circle at 75% 99%, rgba(243, 243, 243, 0.04) 0%, rgba(243, 243, 243, 0.04) 50%, rgba(37, 37, 37, 0.04) 50%, rgba(37, 37, 37, 0.04) 100%), linear-gradient(90deg, #33c4e7, #259cb9);
  color: #fff;
  padding: 1rem;
  margin-bottom: 1rem;
  outline: none;
  border-radius: 0.25rem;
  text-align: left;
  cursor: pointer;
  position: relative; }

p {
  text-align: left; }

details[open] summary ~ * {
  animation: sweep .5s ease-in-out; }

@keyframes sweep {
  0% {
    opacity: 0;
    margin-top: -10px; }
  100% {
    opacity: 1;
    margin-top: 0px; } }
details > summary::after {
  position: absolute;
  content: "+";
  right: 20px; }

details[open] > summary::after {
  position: absolute;
  content: "-";
  right: 20px; }

details > summary::-webkit-details-marker {
  display: none; }

details {
  padding: 0px 100px 0px; }
  @media only screen and (max-width: 56.25em) {
    details {
      padding: 0px 10px 0px; } }

.bosluk {
  margin-top: 20rem; }

.bosluk3 {
  margin-top: 4rem; }

.bosluk3h {
  margin-top: -4rem; }
  @media only screen and (max-width: 56.25em) {
    .bosluk3h {
      margin-top: 0rem; } }

.boslukhm {
  margin-top: 3rem; }

.bosluk333 {
  margin-top: 1rem; }

@media only screen and (max-width: 56.25em) {
  .bosluk4 {
    margin-top: -15rem; } }

@media only screen and (max-width: 56.25em) {
  .bosluk5 {
    margin-top: -40rem; } }

.bosluk6 {
  margin-top: -40rem; }
  @media only screen and (max-width: 56.25em) {
    .bosluk6 {
      margin-top: -40rem; } }

@media only screen and (max-width: 56.25em) {
  .bosluk7 {
    margin-top: 20rem; } }

.bosluk8 {
  margin-top: 9rem; }
  @media screen and (min-width: 1000px) and (max-width: 1024px) {
    .bosluk8 {
      margin-top: 18rem; } }
  @media screen and (min-width: 800px) and (max-width: 812px) {
    .bosluk8 {
      margin-top: 42rem; } }
  @media screen and (min-width: 760px) and (max-width: 768px) {
    .bosluk8 {
      margin-top: -12rem; } }

.bosluk9 {
  margin-top: 2rem; }

.bosluk10 {
  margin-top: 1rem; }

.h2-baslik-hizmetler-yorum {
  font-size: 2.6rem;
  font-weight: 900;
  text-transform: uppercase;
  display: inline-block;
  background-image: linear-gradient(to right, #373075, #2e276a);
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: .1rem; }
  .h2-baslik-hizmetler-yorum__yorum {
    font-size: 1.6rem;
    font-weight: 400;
    color: #797979;
    text-align: center;
    margin-bottom: 1rem; }
    .h2-baslik-hizmetler-yorum__yorum:not(:last-child) {
      margin-bottom: -7rem; }
      @media only screen and (max-width: 56.25em) {
        .h2-baslik-hizmetler-yorum__yorum:not(:last-child) {
          margin-bottom: -9rem;
          text-align: center; } }
    @media only screen and (max-width: 56.25em) {
      .h2-baslik-hizmetler-yorum__yorum {
        font-size: 3.5rem;
        text-align: left;
        padding: 3rem; } }
  @media only screen and (max-width: 56.25em) {
    .h2-baslik-hizmetler-yorum {
      font-size: 5rem; } }

.swiper-container {
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  z-index: 1; }

.swiper-container-no-flexbox .swiper-slide {
  float: left; }

.swiper-container-vertical > .swiper-wrapper {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column; }

.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  -o-transition-property: transform;
  transition-property: transform;
  transition-property: transform,-webkit-transform;
  -webkit-box-sizing: content-box;
  box-sizing: content-box; }

.swiper-container-android .swiper-slide, .swiper-wrapper {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0); }

.swiper-container-multirow > .swiper-wrapper {
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap; }

.swiper-container-free-mode > .swiper-wrapper {
  -webkit-transition-timing-function: ease-out;
  -o-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
  margin: 0 auto; }

.swiper-slide {
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  -o-transition-property: transform;
  transition-property: transform;
  transition-property: transform,-webkit-transform; }

.swiper-slide-invisible-blank {
  visibility: hidden; }

.swiper-container-autoheight, .swiper-container-autoheight .swiper-slide {
  height: auto; }

.swiper-container-autoheight .swiper-wrapper {
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-transition-property: height,-webkit-transform;
  transition-property: height,-webkit-transform;
  -o-transition-property: transform,height;
  transition-property: transform,height;
  transition-property: transform,height,-webkit-transform; }

.swiper-container-3d {
  -webkit-perspective: 1200px;
  perspective: 1200px; }

.swiper-container-3d .swiper-cube-shadow, .swiper-container-3d .swiper-slide, .swiper-container-3d .swiper-slide-shadow-bottom, .swiper-container-3d .swiper-slide-shadow-left, .swiper-container-3d .swiper-slide-shadow-right, .swiper-container-3d .swiper-slide-shadow-top, .swiper-container-3d .swiper-wrapper {
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d; }

.swiper-container-3d .swiper-slide-shadow-bottom, .swiper-container-3d .swiper-slide-shadow-left, .swiper-container-3d .swiper-slide-shadow-right, .swiper-container-3d .swiper-slide-shadow-top {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10; }

.swiper-container-3d .swiper-slide-shadow-left {
  background-image: -webkit-gradient(linear, right top, left top, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0)));
  background-image: -webkit-linear-gradient(right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: -o-linear-gradient(right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0)); }

.swiper-container-3d .swiper-slide-shadow-right {
  background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0)));
  background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0)); }

.swiper-container-3d .swiper-slide-shadow-top {
  background-image: -webkit-gradient(linear, left bottom, left top, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0)));
  background-image: -webkit-linear-gradient(bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: -o-linear-gradient(bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0)); }

.swiper-container-3d .swiper-slide-shadow-bottom {
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0)));
  background-image: -webkit-linear-gradient(top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: -o-linear-gradient(top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0)); }

.swiper-container-wp8-horizontal, .swiper-container-wp8-horizontal > .swiper-wrapper {
  -ms-touch-action: pan-y;
  touch-action: pan-y; }

.swiper-container-wp8-vertical, .swiper-container-wp8-vertical > .swiper-wrapper {
  -ms-touch-action: pan-x;
  touch-action: pan-x; }

.swiper-button-next, .swiper-button-prev {
  position: absolute;
  top: 50%;
  width: 27px;
  height: 44px;
  margin-top: -22px;
  z-index: 10;
  cursor: pointer;
  background-size: 27px 44px;
  background-position: center;
  background-repeat: no-repeat; }

.swiper-button-next.swiper-button-disabled, .swiper-button-prev.swiper-button-disabled {
  opacity: .35;
  cursor: auto;
  pointer-events: none; }

.swiper-button-prev, .swiper-container-rtl .swiper-button-next {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23007aff'%2F%3E%3C%2Fsvg%3E");
  left: 10px;
  right: auto; }

.swiper-button-next, .swiper-container-rtl .swiper-button-prev {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23007aff'%2F%3E%3C%2Fsvg%3E");
  right: 10px;
  left: auto; }

.swiper-button-prev.swiper-button-white, .swiper-container-rtl .swiper-button-next.swiper-button-white {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23ffffff'%2F%3E%3C%2Fsvg%3E"); }

.swiper-button-next.swiper-button-white, .swiper-container-rtl .swiper-button-prev.swiper-button-white {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23ffffff'%2F%3E%3C%2Fsvg%3E"); }

.swiper-button-prev.swiper-button-black, .swiper-container-rtl .swiper-button-next.swiper-button-black {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23000000'%2F%3E%3C%2Fsvg%3E"); }

.swiper-button-next.swiper-button-black, .swiper-container-rtl .swiper-button-prev.swiper-button-black {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23000000'%2F%3E%3C%2Fsvg%3E"); }

.swiper-button-lock {
  display: none; }

.swiper-pagination {
  position: absolute;
  text-align: center;
  -webkit-transition: .3s opacity;
  -o-transition: .3s opacity;
  transition: .3s opacity;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  z-index: 10; }

.swiper-pagination.swiper-pagination-hidden {
  opacity: 0; }

.swiper-container-horizontal > .swiper-pagination-bullets, .swiper-pagination-custom, .swiper-pagination-fraction {
  bottom: 10px;
  left: 0;
  width: 100%; }

.swiper-pagination-bullets-dynamic {
  overflow: hidden;
  font-size: 0; }

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  -webkit-transform: scale(0.33);
  -ms-transform: scale(0.33);
  transform: scale(0.33);
  position: relative; }

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1); }

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1); }

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
  -webkit-transform: scale(0.66);
  -ms-transform: scale(0.66);
  transform: scale(0.66); }

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
  -webkit-transform: scale(0.33);
  -ms-transform: scale(0.33);
  transform: scale(0.33); }

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
  -webkit-transform: scale(0.66);
  -ms-transform: scale(0.66);
  transform: scale(0.66); }

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
  -webkit-transform: scale(0.33);
  -ms-transform: scale(0.33);
  transform: scale(0.33); }

.swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  display: inline-block;
  border-radius: 100%;
  background: #000;
  opacity: .2; }

button.swiper-pagination-bullet {
  border: none;
  margin: 0;
  padding: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none; }

.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer; }

.swiper-pagination-bullet-active {
  opacity: 1;
  background: #007aff; }

.swiper-container-vertical > .swiper-pagination-bullets {
  right: 10px;
  top: 50%;
  -webkit-transform: translate3d(0, -50%, 0);
  transform: translate3d(0, -50%, 0); }

.swiper-container-vertical > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 6px 0;
  display: block; }

.swiper-container-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 8px; }

.swiper-container-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  display: inline-block;
  -webkit-transition: .2s top,.2s -webkit-transform;
  transition: .2s top,.2s -webkit-transform;
  -o-transition: .2s transform,.2s top;
  transition: .2s transform,.2s top;
  transition: .2s transform,.2s top,.2s -webkit-transform; }

.swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 4px; }

.swiper-container-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  white-space: nowrap; }

.swiper-container-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  -webkit-transition: .2s left,.2s -webkit-transform;
  transition: .2s left,.2s -webkit-transform;
  -o-transition: .2s transform,.2s left;
  transition: .2s transform,.2s left;
  transition: .2s transform,.2s left,.2s -webkit-transform; }

.swiper-container-horizontal.swiper-container-rtl > .swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  -webkit-transition: .2s right,.2s -webkit-transform;
  transition: .2s right,.2s -webkit-transform;
  -o-transition: .2s transform,.2s right;
  transition: .2s transform,.2s right;
  transition: .2s transform,.2s right,.2s -webkit-transform; }

.swiper-pagination-progressbar {
  background: rgba(0, 0, 0, 0.25);
  position: absolute; }

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  background: #007aff;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  -webkit-transform-origin: left top;
  -ms-transform-origin: left top;
  transform-origin: left top; }

.swiper-container-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  -webkit-transform-origin: right top;
  -ms-transform-origin: right top;
  transform-origin: right top; }

.swiper-container-horizontal > .swiper-pagination-progressbar, .swiper-container-vertical > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite {
  width: 100%;
  height: 4px;
  left: 0;
  top: 0; }

.swiper-container-horizontal > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite, .swiper-container-vertical > .swiper-pagination-progressbar {
  width: 4px;
  height: 100%;
  left: 0;
  top: 0; }

.swiper-pagination-white .swiper-pagination-bullet-active {
  background: #fff; }

.swiper-pagination-progressbar.swiper-pagination-white {
  background: rgba(255, 255, 255, 0.25); }

.swiper-pagination-progressbar.swiper-pagination-white .swiper-pagination-progressbar-fill {
  background: #fff; }

.swiper-pagination-black .swiper-pagination-bullet-active {
  background: #000; }

.swiper-pagination-progressbar.swiper-pagination-black {
  background: rgba(0, 0, 0, 0.25); }

.swiper-pagination-progressbar.swiper-pagination-black .swiper-pagination-progressbar-fill {
  background: #000; }

.swiper-pagination-lock {
  display: none; }

.swiper-scrollbar {
  border-radius: 10px;
  position: relative;
  -ms-touch-action: none;
  background: rgba(0, 0, 0, 0.1); }

.swiper-container-horizontal > .swiper-scrollbar {
  position: absolute;
  left: 1%;
  bottom: 3px;
  z-index: 50;
  height: 5px;
  width: 98%; }

.swiper-container-vertical > .swiper-scrollbar {
  position: absolute;
  right: 3px;
  top: 1%;
  z-index: 50;
  width: 5px;
  height: 98%; }

.swiper-scrollbar-drag {
  height: 100%;
  width: 100%;
  position: relative;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  left: 0;
  top: 0; }

.swiper-scrollbar-cursor-drag {
  cursor: move; }

.swiper-scrollbar-lock {
  display: none; }

.swiper-zoom-container {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: center; }

.swiper-zoom-container > canvas, .swiper-zoom-container > img, .swiper-zoom-container > svg {
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: contain;
  object-fit: contain; }

.swiper-slide-zoomed {
  cursor: move; }

.swiper-lazy-preloader {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  -webkit-transform-origin: 50%;
  -ms-transform-origin: 50%;
  transform-origin: 50%;
  -webkit-animation: swiper-preloader-spin 1s steps(12, end) infinite;
  animation: swiper-preloader-spin 1s steps(12, end) infinite; }

.swiper-lazy-preloader:after {
  display: block;
  content: '';
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20viewBox%3D'0%200%20120%20120'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20xmlns%3Axlink%3D'http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink'%3E%3Cdefs%3E%3Cline%20id%3D'l'%20x1%3D'60'%20x2%3D'60'%20y1%3D'7'%20y2%3D'27'%20stroke%3D'%236c6c6c'%20stroke-width%3D'11'%20stroke-linecap%3D'round'%2F%3E%3C%2Fdefs%3E%3Cg%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(30%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(60%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(90%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(120%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(150%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.37'%20transform%3D'rotate(180%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.46'%20transform%3D'rotate(210%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.56'%20transform%3D'rotate(240%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.66'%20transform%3D'rotate(270%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.75'%20transform%3D'rotate(300%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.85'%20transform%3D'rotate(330%2060%2C60)'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E");
  background-position: 50%;
  background-size: 100%;
  background-repeat: no-repeat; }

.swiper-lazy-preloader-white:after {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20viewBox%3D'0%200%20120%20120'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20xmlns%3Axlink%3D'http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink'%3E%3Cdefs%3E%3Cline%20id%3D'l'%20x1%3D'60'%20x2%3D'60'%20y1%3D'7'%20y2%3D'27'%20stroke%3D'%23fff'%20stroke-width%3D'11'%20stroke-linecap%3D'round'%2F%3E%3C%2Fdefs%3E%3Cg%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(30%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(60%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(90%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(120%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(150%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.37'%20transform%3D'rotate(180%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.46'%20transform%3D'rotate(210%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.56'%20transform%3D'rotate(240%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.66'%20transform%3D'rotate(270%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.75'%20transform%3D'rotate(300%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.85'%20transform%3D'rotate(330%2060%2C60)'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E"); }

@-webkit-keyframes swiper-preloader-spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg); } }
@keyframes swiper-preloader-spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg); } }
.swiper-container .swiper-notification {
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  opacity: 0;
  z-index: -1000; }

.swiper-container-fade.swiper-container-free-mode .swiper-slide {
  -webkit-transition-timing-function: ease-out;
  -o-transition-timing-function: ease-out;
  transition-timing-function: ease-out; }

.swiper-container-fade .swiper-slide {
  pointer-events: none;
  -webkit-transition-property: opacity;
  -o-transition-property: opacity;
  transition-property: opacity; }

.swiper-container-fade .swiper-slide .swiper-slide {
  pointer-events: none; }

.swiper-container-fade .swiper-slide-active, .swiper-container-fade .swiper-slide-active .swiper-slide-active {
  pointer-events: auto; }

.swiper-container-cube {
  overflow: visible; }

.swiper-container-cube .swiper-slide {
  pointer-events: none;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  z-index: 1;
  visibility: hidden;
  -webkit-transform-origin: 0 0;
  -ms-transform-origin: 0 0;
  transform-origin: 0 0;
  width: 100%;
  height: 100%; }

.swiper-container-cube .swiper-slide .swiper-slide {
  pointer-events: none; }

.swiper-container-cube.swiper-container-rtl .swiper-slide {
  -webkit-transform-origin: 100% 0;
  -ms-transform-origin: 100% 0;
  transform-origin: 100% 0; }

.swiper-container-cube .swiper-slide-active, .swiper-container-cube .swiper-slide-active .swiper-slide-active {
  pointer-events: auto; }

.swiper-container-cube .swiper-slide-active, .swiper-container-cube .swiper-slide-next, .swiper-container-cube .swiper-slide-next + .swiper-slide, .swiper-container-cube .swiper-slide-prev {
  pointer-events: auto;
  visibility: visible; }

.swiper-container-cube .swiper-slide-shadow-bottom, .swiper-container-cube .swiper-slide-shadow-left, .swiper-container-cube .swiper-slide-shadow-right, .swiper-container-cube .swiper-slide-shadow-top {
  z-index: 0;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden; }

.swiper-container-cube .swiper-cube-shadow {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: .6;
  -webkit-filter: blur(50px);
  filter: blur(50px);
  z-index: 0; }

.swiper-container-flip {
  overflow: visible; }

.swiper-container-flip .swiper-slide {
  pointer-events: none;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  z-index: 1; }

.swiper-container-flip .swiper-slide .swiper-slide {
  pointer-events: none; }

.swiper-container-flip .swiper-slide-active, .swiper-container-flip .swiper-slide-active .swiper-slide-active {
  pointer-events: auto; }

.swiper-container-flip .swiper-slide-shadow-bottom, .swiper-container-flip .swiper-slide-shadow-left, .swiper-container-flip .swiper-slide-shadow-right, .swiper-container-flip .swiper-slide-shadow-top {
  z-index: 0;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden; }

.swiper-container-coverflow .swiper-wrapper {
  -ms-perspective: 1200px; }

.header {
  height: 12vh;
  width: 100%;
  background-size: cover;
  background-position: top;
  position: fixed;
  background: #fff;
  z-index: 2;
  /*position: relative;*/ }
  @media only screen and (max-width: 40em) {
    .header {
      height: 11vh; } }
  @media only screen and (max-width: 390px) {
    .header {
      height: 14vh; } }
  .header__logo-alani {
    position: absolute;
    top: 2rem;
    left: 4rem;
    z-index: 9; }
  .header__logo {
    /*height: 20px;*/ }

.header-slider {
  height: 94vh;
  background-size: cover;
  background-position: top;
  /*position: relative;*/ }
  @media only screen and (max-width: 40em) {
    .header-slider {
      /*height: 20vh;*/
      /*margin-top: -300px;*/ } }
  @media only screen and (max-width: 1024px) {
    .header-slider {
      height: 39vh; } }

.header__slider-alani {
  position: absolute;
  top: 40%;
  left: 42%;
  transform: translate(-30%, -30%);
  text-align: center; }
  @media only screen and (max-width: 40em) {
    .header__slider-alani {
      position: absolute;
      top: 20%;
      left: 35%;
      transform: translate(-30%, -30%);
      text-align: center; } }

.header-area {
  height: 36vh;
  background: #000000;
  background-size: cover;
  background-position: top;
  position: relative; }
  @media only screen and (max-width: 40em) {
    .header-area {
      height: 28vh; } }

.header-area-top {
  position: absolute;
  top: 70%;
  left: 47%;
  transform: translate(-30%, -30%);
  text-align: center;
  z-index: inherit; }
  @media only screen and (max-width: 40em) {
    .header-area-top {
      position: absolute;
      top: 41%;
      left: 40%;
      transform: translate(-30%, -30%);
      text-align: center; } }
  @media only screen and (max-width: 56.25em) {
    .header-area-top {
      position: absolute;
      top: 70%;
      left: 45%;
      transform: translate(-30%, -30%);
      text-align: center; } }

.header-area-top-titles {
  color: #fff;
  text-transform: uppercase;
  margin-bottom: 43rem;
  backface-visibility: hidden; }
  @media only screen and (max-width: 56.25em) {
    .header-area-top-titles {
      font-size: 8rem; } }
  .header-area-top-titles--1 {
    display: block;
    font-size: 4rem;
    font-weight: 900;
    letter-spacing: 0.3rem;
    animation-name: solahareket;
    animation-duration: 1s;
    animation-timing-function: ease-in-out; }
    @media only screen and (max-width: 56.25em) {
      .header-area-top-titles--1 {
        font-size: 4rem; } }
    @media only screen and (max-width: 56.25em) {
      .header-area-top-titles--1 {
        font-size: 4rem; } }
    @media only screen and (max-width: 75em) {
      .header-area-top-titles--1 {
        font-size: 4rem; } }
  .header-area-top-titles--2 {
    display: block;
    font-size: 1.5rem;
    font-weight: 200;
    letter-spacing: 0rem;
    font-weight: 500;
    animation: sagahareket 1s ease-out; }
    @media only screen and (max-width: 56.25em) {
      .header-area-top-titles--2 {
        font-size: 3rem;
        font-weight: 500; } }

.header-area-top-blog {
  position: absolute;
  top: 70%;
  left: 48%;
  transform: translate(-30%, -30%);
  text-align: center; }
  @media only screen and (max-width: 40em) {
    .header-area-top-blog {
      position: absolute;
      top: 41%;
      left: 40%;
      transform: translate(-30%, -30%);
      text-align: center; } }
  @media only screen and (max-width: 56.25em) {
    .header-area-top-blog {
      position: absolute;
      top: 70%;
      left: 45%;
      transform: translate(-30%, -30%);
      text-align: center; } }

.header-contact-area {
  height: 68vh;
  background: #000;
  background-size: cover;
  background-position: top;
  position: relative;
  z-index: inherit; }
  @media only screen and (max-width: 40em) {
    .header-contact-area {
      height: 69vh; } }

.header-contact-area-top {
  position: absolute;
  top: 60%;
  left: 43%;
  transform: translate(-30%, -30%);
  text-align: center; }
  @media only screen and (max-width: 40em) {
    .header-contact-area-top {
      position: absolute;
      top: 41%;
      left: 40%;
      transform: translate(-30%, -30%);
      text-align: center; } }
  @media only screen and (max-width: 56.25em) {
    .header-contact-area-top {
      position: absolute;
      top: 60%;
      left: 40%;
      transform: translate(-30%, -30%);
      text-align: center; } }

.header-contact-area-top-titles {
  color: #fff;
  text-transform: uppercase;
  margin-bottom: 5rem;
  backface-visibility: hidden; }
  @media only screen and (max-width: 56.25em) {
    .header-contact-area-top-titles {
      font-size: 5rem; } }
  .header-contact-area-top-titles--1 {
    display: block;
    font-size: 3rem;
    font-weight: 900;
    letter-spacing: 1rem;
    animation-name: solahareket;
    animation-duration: 1s;
    animation-timing-function: ease-in-out; }
    @media only screen and (max-width: 56.25em) {
      .header-contact-area-top-titles--1 {
        font-size: 4rem; } }
    @media only screen and (max-width: 56.25em) {
      .header-contact-area-top-titles--1 {
        font-size: 4rem; } }
    @media only screen and (max-width: 75em) {
      .header-contact-area-top-titles--1 {
        font-size: 4rem; } }
  .header-contact-area-top-titles--2 {
    display: block;
    font-size: 1.5rem;
    font-weight: 200;
    letter-spacing: 0rem;
    font-weight: 500;
    animation: sagahareket 1s ease-out; }
    @media only screen and (max-width: 56.25em) {
      .header-contact-area-top-titles--2 {
        font-size: 3rem;
        font-weight: 500; } }

.tablolar {
  margin-top: 1rem; }

.tablo {
  max-width: 133rem;
  margin: 0 auto;
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap; }
  .tablo:not(:last-child) {
    margin-bottom: 8rem; }
    @media only screen and (max-width: 56.25em) {
      .tablo:not(:last-child) {
        margin-bottom: 6rem;
        margin-top: -16rem; } }
  @media only screen and (max-width: 56.25em) {
    .tablo {
      padding: 20rem 5rem; } }
  .tablo::after {
    content: "";
    display: table;
    clear: both; }
  .tablo [class^="tablo--"] {
    float: left; }
    .tablo [class^="tablo--"]:not(:last-child) {
      margin-right: 6rem; }
      @media only screen and (max-width: 56.25em) {
        .tablo [class^="tablo--"]:not(:last-child) {
          margin-right: 0;
          margin-bottom: 6rem; } }
    @media only screen and (max-width: 56.25em) {
      .tablo [class^="tablo--"] {
        width: 100% !important; } }
  .tablo .tablo--1-ve-2 {
    width: calc((100% - 6rem) / 2); }
  .tablo .tablo--1-ve-3 {
    width: calc((100% - 2 * 6rem) / 3); }
  .tablo .tablo--1-ve-4 {
    width: calc((100% - 3 * 6rem) /4); }
  .tablo .tablo--2-ve-3 {
    width: calc(2* ((100% - 2 * 6rem) /3) + 6rem); }
  .tablo .tablo--2-ve-4 {
    width: calc(2* ((100% - 3 * 6rem) /4) + 6rem); }
  .tablo .tablo--3-ve-4 {
    width: calc(3* ((100% - 3* 6rem) /4) + 2 * 6rem); }

.tablohakkimizda {
  max-width: 114rem;
  margin: 0 auto; }
  .tablohakkimizda:not(:last-child) {
    margin-bottom: 8rem; }
    @media only screen and (max-width: 56.25em) {
      .tablohakkimizda:not(:last-child) {
        margin-bottom: -33rem;
        margin-top: -16rem; } }
  @media only screen and (max-width: 56.25em) {
    .tablohakkimizda {
      padding: 20rem 5rem; } }
  .tablohakkimizda::after {
    content: "";
    display: table;
    clear: both; }
  .tablohakkimizda [class^="tablo--"] {
    float: left; }
    .tablohakkimizda [class^="tablo--"]:not(:last-child) {
      margin-right: 6rem; }
      @media only screen and (max-width: 56.25em) {
        .tablohakkimizda [class^="tablo--"]:not(:last-child) {
          margin-right: 0;
          margin-bottom: 6rem; } }
    @media only screen and (max-width: 56.25em) {
      .tablohakkimizda [class^="tablo--"] {
        width: 100% !important; } }
  .tablohakkimizda .tablo--1-ve-2 {
    width: calc((100% - 6rem) / 2); }
  .tablohakkimizda .tablo--1-ve-3 {
    width: calc((100% - 2 * 6rem) / 3); }
  .tablohakkimizda .tablo--1-ve-4 {
    width: calc((100% - 3 * 6rem) /4); }
  .tablohakkimizda .tablo--2-ve-3 {
    width: calc(2* ((100% - 2 * 6rem) /3) + 6rem); }
  .tablohakkimizda .tablo--2-ve-4 {
    width: calc(2* ((100% - 3 * 6rem) /4) + 6rem); }
  .tablohakkimizda .tablo--3-ve-4 {
    width: calc(3* ((100% - 3* 6rem) /4) + 2 * 6rem); }

.tablohizmetler {
  max-width: 133rem;
  margin: 0 auto;
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap; }
  @media only screen and (max-width: 1180px) {
    .tablohizmetler {
      max-width: 133rem; } }
  .tablohizmetler:not(:last-child) {
    margin-bottom: 8rem; }
    @media only screen and (max-width: 56.25em) {
      .tablohizmetler:not(:last-child) {
        margin-bottom: -33rem;
        margin-top: -16rem; } }
  @media only screen and (max-width: 56.25em) {
    .tablohizmetler {
      padding: 20rem 5rem; } }
  .tablohizmetler::after {
    content: "";
    display: table;
    clear: both; }
  .tablohizmetler [class^="tablo--"] {
    float: left; }
    .tablohizmetler [class^="tablo--"]:not(:last-child) {
      margin-right: 6rem; }
      @media only screen and (max-width: 56.25em) {
        .tablohizmetler [class^="tablo--"]:not(:last-child) {
          margin-right: 0;
          margin-bottom: 6rem; } }
    @media only screen and (max-width: 56.25em) {
      .tablohizmetler [class^="tablo--"] {
        width: 100% !important; } }
  .tablohizmetler .tablo--1-ve-2 {
    width: calc((100% - 6rem) / 2); }
  .tablohizmetler .tablo--1-ve-3 {
    width: calc((100% - 2 * 6rem) / 3); }
  .tablohizmetler .tablo--1-ve-4 {
    width: calc((100% - 3 * 6rem) /4); }
  .tablohizmetler .tablo--2-ve-3 {
    width: calc(2* ((100% - 2 * 6rem) /3) + 6rem); }
  .tablohizmetler .tablo--2-ve-4 {
    width: calc(2* ((100% - 3 * 6rem) /4) + 6rem); }
  .tablohizmetler .tablo--3-ve-4 {
    width: calc(3* ((100% - 3* 6rem) /4) + 2 * 6rem); }

.tablohizmetlerdetay {
  max-width: 114rem;
  margin: 0 auto; }
  .tablohizmetlerdetay:not(:last-child) {
    margin-bottom: 8rem; }
    @media only screen and (max-width: 56.25em) {
      .tablohizmetlerdetay:not(:last-child) {
        margin-bottom: -33rem;
        margin-top: -16rem; } }
  @media only screen and (max-width: 56.25em) {
    .tablohizmetlerdetay {
      padding: 20rem 5rem; } }
  .tablohizmetlerdetay::after {
    content: "";
    display: table;
    clear: both; }
  .tablohizmetlerdetay [class^="tablo--"] {
    float: left; }
    .tablohizmetlerdetay [class^="tablo--"]:not(:last-child) {
      margin-right: 6rem; }
      @media only screen and (max-width: 56.25em) {
        .tablohizmetlerdetay [class^="tablo--"]:not(:last-child) {
          margin-right: 0;
          margin-bottom: 6rem; } }
    @media only screen and (max-width: 56.25em) {
      .tablohizmetlerdetay [class^="tablo--"] {
        width: 100% !important; } }
  .tablohizmetlerdetay .tablo--1-ve-2 {
    width: calc((100% - 6rem) / 2); }
  .tablohizmetlerdetay .tablo--1-ve-3 {
    width: calc((100% - 2 * 6rem) / 3); }
  .tablohizmetlerdetay .tablo--1-ve-4 {
    width: calc((100% - 3 * 6rem) /4); }
  .tablohizmetlerdetay .tablo--2-ve-3 {
    width: calc(2* ((100% - 2 * 6rem) /3) + 6rem); }
  .tablohizmetlerdetay .tablo--2-ve-4 {
    width: calc(2* ((100% - 3 * 6rem) /4) + 6rem); }
  .tablohizmetlerdetay .tablo--3-ve-4 {
    width: calc(3* ((100% - 3* 6rem) /4) + 2 * 6rem); }

.tabloyorumlar {
  max-width: 114rem;
  margin: 0 auto; }
  .tabloyorumlar:not(:last-child) {
    margin-bottom: 8rem; }
    @media only screen and (max-width: 56.25em) {
      .tabloyorumlar:not(:last-child) {
        margin-bottom: -33rem;
        margin-top: -16rem; } }
  @media only screen and (max-width: 56.25em) {
    .tabloyorumlar {
      padding: 20rem 5rem; } }
  .tabloyorumlar::after {
    content: "";
    display: table;
    clear: both; }
  .tabloyorumlar [class^="tablo--"] {
    float: left; }
    .tabloyorumlar [class^="tablo--"]:not(:last-child) {
      margin-right: 6rem; }
      @media only screen and (max-width: 56.25em) {
        .tabloyorumlar [class^="tablo--"]:not(:last-child) {
          margin-right: 0;
          margin-bottom: 6rem; } }
    @media only screen and (max-width: 56.25em) {
      .tabloyorumlar [class^="tablo--"] {
        width: 100% !important; } }
  .tabloyorumlar .tablo--1-ve-2 {
    width: calc((100% - 6rem) / 2); }
  .tabloyorumlar .tablo--1-ve-3 {
    width: calc((100% - 2 * 6rem) / 3); }
  .tabloyorumlar .tablo--1-ve-4 {
    width: calc((100% - 3 * 6rem) /4); }
  .tabloyorumlar .tablo--2-ve-3 {
    width: calc(2* ((100% - 2 * 6rem) /3) + 6rem); }
  .tabloyorumlar .tablo--2-ve-4 {
    width: calc(2* ((100% - 3 * 6rem) /4) + 6rem); }
  .tabloyorumlar .tablo--3-ve-4 {
    width: calc(3* ((100% - 3* 6rem) /4) + 2 * 6rem); }

.tablo-footer {
  max-width: 151rem;
  margin: 0 auto; }
  .tablo-footer:not(:last-child) {
    margin-bottom: 8rem;
    margin-right: 0rem; }
    @media only screen and (max-width: 56.25em) {
      .tablo-footer:not(:last-child) {
        margin-bottom: 6rem;
        margin-top: -16rem; } }
  @media only screen and (max-width: 56.25em) {
    .tablo-footer {
      padding: 20rem 5rem; } }
  .tablo-footer::after {
    content: "";
    display: table;
    clear: both; }
  .tablo-footer [class^="tablo--"] {
    float: left; }
    .tablo-footer [class^="tablo--"]:not(:last-child) {
      margin-right: 6rem; }
      @media only screen and (max-width: 56.25em) {
        .tablo-footer [class^="tablo--"]:not(:last-child) {
          margin-right: 0;
          margin-bottom: 6rem; } }
    @media only screen and (max-width: 56.25em) {
      .tablo-footer [class^="tablo--"] {
        width: 100% !important; } }
  .tablo-footer .tablo--1-ve-2 {
    width: calc((100% - 6rem) / 2); }
  .tablo-footer .tablo--1-ve-3 {
    width: calc((100% - 2 * 6rem) / 3); }
  .tablo-footer .tablo--1-ve-4 {
    width: calc((100% - 3 * 6rem) /4); }
  .tablo-footer .tablo--2-ve-3 {
    width: calc(2* ((100% - 2 * 6rem) /3) + 6rem); }
  .tablo-footer .tablo--2-ve-4 {
    width: calc(2* ((100% - 3 * 6rem) /4) + 6rem); }
  .tablo-footer .tablo--3-ve-4 {
    width: calc(3* ((100% - 3* 6rem) /4) + 2 * 6rem); }

.tabloservices {
  max-width: 103rem;
  margin: 0 auto;
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap; }
  @media only screen and (max-width: 1180px) {
    .tabloservices {
      max-width: 133rem; } }
  .tabloservices:not(:last-child) {
    margin-bottom: 8rem; }
    @media only screen and (max-width: 56.25em) {
      .tabloservices:not(:last-child) {
        margin-bottom: -33rem;
        margin-top: -16rem; } }
  @media only screen and (max-width: 56.25em) {
    .tabloservices {
      padding: 20rem 5rem; } }
  .tabloservices::after {
    content: "";
    display: table;
    clear: both; }
  .tabloservices [class^="tablo--"] {
    float: left; }
    .tabloservices [class^="tablo--"]:not(:last-child) {
      margin-right: 6rem; }
      @media only screen and (max-width: 56.25em) {
        .tabloservices [class^="tablo--"]:not(:last-child) {
          margin-right: 0;
          margin-bottom: 6rem; } }
    @media only screen and (max-width: 56.25em) {
      .tabloservices [class^="tablo--"] {
        width: 100% !important; } }
  .tabloservices .tablo--1-ve-2 {
    width: calc((100% - 6rem) / 2); }
  .tabloservices .tablo--1-ve-3 {
    width: calc((100% - 2 * 6rem) / 3); }
  .tabloservices .tablo--1-ve-4 {
    width: calc((100% - 3 * 6rem) /4); }
  .tabloservices .tablo--2-ve-3 {
    width: calc(2* ((100% - 2 * 6rem) /3) + 6rem); }
  .tabloservices .tablo--2-ve-4 {
    width: calc(2* ((100% - 3 * 6rem) /4) + 6rem); }
  .tabloservices .tablo--3-ve-4 {
    width: calc(3* ((100% - 3* 6rem) /4) + 2 * 6rem); }

/* Slider Buton */
.buton, .buton:link, .buton:visited {
  text-transform: uppercase;
  text-decoration: none;
  padding: 1.5rem 4rem;
  display: inline-block;
  transition: all 2s;
  position: relative;
  font-size: 1.2rem;
  cursor: pointer; }
  @media only screen and (max-width: 56.25em) {
    .buton, .buton:link, .buton:visited {
      font-size: 3.6rem;
      padding: 3.5rem 6rem; } }
.buton:hover {
  transform: translateY(-0.3rem);
  box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.2); }
  .buton:hover::after {
    transform: scaleX(1.4) scaleY(1.6);
    opacity: 0; }
.buton:active, .buton:focus {
  outline: none;
  transform: translateY(-0.1rem);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.2);
  border: none; }
.buton--beyaz {
  background-color: #fff;
  color: #777; }
  .buton--beyaz::after {
    background-color: #fff; }
.buton--siyah {
  background-color: #444;
  color: #fff; }
  .buton--siyah::after {
    background-color: #444; }
.buton--pembe-slider {
  background-color: #DE6FAA;
  color: #fff;
  margin-top: 22.5rem;
  left: -26rem; }
  @media only screen and (max-width: 1366px) {
    .buton--pembe-slider {
      background-color: #DE6FAA;
      color: #fff;
      margin-top: 29.5rem;
      left: -26rem; } }
  @media only screen and (max-width: 40em) {
    .buton--pembe-slider {
      margin-top: 205.5rem;
      left: -39rem; } }
  @media only screen and (max-width: 75em) {
    .buton--pembe-slider {
      /*left:-15px;*/
      margin-top: 205.5rem;
      left: -40rem; } }
  .buton--pembe-slider::after {
    background-color: #DE6FAA; }
.buton--pembe {
  background-color: #DE6FAA;
  color: #fff; }
  .buton--pembe::after {
    background-color: #DE6FAA; }
.buton--kirmizi {
  background-color: #373075;
  color: #fff;
  border: none; }
  .buton--kirmizi::after {
    background-color: #2e276a; }
.buton--kirmizislider {
  background-color: #373075;
  color: #fff;
  margin-right: 82rem; }
  .buton--kirmizislider::after {
    background-color: #2e276a; }
.buton::after {
  content: "";
  display: inline-block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  transition: all .4s; }
.buton--animasyon {
  animation: butonefekt .5s ease-out .75s;
  animation-fill-mode: backwards; }

.h-buton-link:link, .h-buton-link:visited {
  font-size: 1.7rem;
  color: #777;
  display: inline-block;
  text-decoration: none;
  border-bottom: 0.1rem solid #777;
  padding: .3rem;
  transition: all .2s; }
  @media only screen and (max-width: 56.25em) {
    .h-buton-link:link, .h-buton-link:visited {
      font-size: 4.7rem; } }
.h-buton-link:hover {
  background-color: #2e276a;
  color: #fff;
  box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
  transform: translateY(-0.2rem); }
.h-buton-link:active {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  transform: translateY(0); }

.buton1, .buton1:link, .buton1:visited {
  text-transform: uppercase;
  text-decoration: none;
  padding: 1.5rem 4rem;
  display: inline-block;
  transition: all 2s;
  position: relative;
  font-size: 1.2rem;
  cursor: pointer;
  text-align: center; }
  @media only screen and (max-width: 56.25em) {
    .buton1, .buton1:link, .buton1:visited {
      font-size: 2.6rem;
      padding: 1.5rem 0rem; } }
  @media only screen and (max-width: 812px) {
    .buton1, .buton1:link, .buton1:visited {
      font-size: 3rem; } }
.buton1:hover {
  transform: translateY(-0.3rem);
  box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.2); }
  .buton1:hover::after {
    transform: scaleX(1.4) scaleY(1.6);
    opacity: 0; }
.buton1:active, .buton1:focus {
  outline: none;
  transform: translateY(-0.1rem);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.2); }
.buton1--beyaz {
  background-color: #fff;
  color: #777; }
  .buton1--beyaz::after {
    background-color: #fff; }
.buton1--siyah {
  background-color: #444;
  color: #fff; }
  .buton1--siyah::after {
    background-color: #444; }
.buton1--pembe-slider {
  background-color: #DE6FAA;
  color: #fff;
  margin-top: 22.5rem;
  left: -26rem; }
  @media only screen and (max-width: 1366px) {
    .buton1--pembe-slider {
      background-color: #DE6FAA;
      color: #fff;
      margin-top: 29.5rem;
      left: -26rem; } }
  @media only screen and (max-width: 40em) {
    .buton1--pembe-slider {
      margin-top: 205.5rem;
      left: -39rem; } }
  @media only screen and (max-width: 75em) {
    .buton1--pembe-slider {
      /*left:-15px;*/
      margin-top: 205.5rem;
      left: -40rem; } }
  .buton1--pembe-slider::after {
    background-color: #DE6FAA; }
.buton1--pembe {
  background-color: #DE6FAA;
  color: #fff; }
  .buton1--pembe::after {
    background-color: #DE6FAA; }
.buton1--kirmizi {
  background-color: #373075;
  color: #fff; }
  .buton1--kirmizi::after {
    background-color: #2e276a; }
.buton1--kirmizislider {
  background-color: #202020;
  color: #fff; }
  .buton1--kirmizislider::after {
    background-color: #202020; }
.buton1::after {
  content: "";
  display: inline-block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  transition: all .4s; }
.buton1--animasyon {
  animation: butonefekt .5s ease-out .75s;
  animation-fill-mode: backwards; }

.marka {
  height: 12.5rem;
  margin-left: 4rem;
  filter: alpha(opacity=40);
  opacity: 0.9;
  transition: all .2s ease-out; }
  @media only screen and (max-width: 56.25em) {
    .marka {
      height: 23.5rem;
      margin-left: 0rem; } }
  .marka:hover {
    height: 12.5rem;
    margin-left: 4rem;
    opacity: 1;
    transform: translateY(-0.3rem); }
    @media only screen and (max-width: 56.25em) {
      .marka:hover {
        height: 23.5rem; } }

.galeri {
  position: relative; }
  .galeri__gorsel {
    width: 100%;
    /*height: 55rem;*/
    outline-offset: 2rem;
    z-index: 10; }
    @media only screen and (max-width: 56.25em) {
      .galeri__gorsel {
        float: left;
        height: 100%; } }
    .galeri__gorsel--3 {
      left: 20%;
      margin-top: -1rem;
      /*top: 10rem;*/ }
    .galeri__gorsel--8 {
      left: 20%;
      margin-top: -1rem;
      /*top: 10rem;*/ }
      @media only screen and (max-width: 1024px) {
        .galeri__gorsel--8 {
          margin-top: 1rem; } }
      @media only screen and (max-width: 56.25em) {
        .galeri__gorsel--8 {
          margin-top: 60rem; } }
      @media only screen and (max-width: 40em) {
        .galeri__gorsel--8 {
          margin-top: 58rem; } }
    .galeri__gorsel--4 {
      left: 20%;
      margin-top: -5rem;
      /*top: 10rem;*/ }
      @media only screen and (max-width: 56.25em) {
        .galeri__gorsel--4 {
          margin-top: 18rem; } }

.galeriprojedetay {
  position: relative; }
  .galeriprojedetay__gorsel {
    width: 100%;
    height: 40rem;
    outline-offset: 2rem;
    z-index: 10;
    transition: all .2s; }
    @media only screen and (max-width: 56.25em) {
      .galeriprojedetay__gorsel {
        float: left;
        height: 100%;
        box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.2); } }
    .galeriprojedetay__gorsel--3 {
      left: 20%;
      margin-top: -1rem;
      /*top: 10rem;*/ }
      @media only screen and (max-width: 56.25em) {
        .galeriprojedetay__gorsel--3 {
          margin-top: .1rem; } }
    .galeriprojedetay__gorsel--4 {
      left: 20%;
      margin-top: -5rem;
      /*top: 10rem;*/ }
      @media only screen and (max-width: 56.25em) {
        .galeriprojedetay__gorsel--4 {
          margin-top: 11rem; } }
  .galeriprojedetay:hover .galeriprojedetay__gorsel:not(:hover) {
    transform: scale(0.95); }

.galeri {
  position: relative; }
  .galeri__popup {
    width: 100%;
    height: 100%;
    outline-offset: 2rem;
    z-index: 10;
    transition: all .2s; }
    @media only screen and (max-width: 56.25em) {
      .galeri__popup {
        float: left;
        height: 100%;
        box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.2); } }
    .galeri__popup--1 {
      margin-top: -1rem;
      /*top: 10rem;*/ }
      @media only screen and (max-width: 56.25em) {
        .galeri__popup--1 {
          margin-top: -1rem; } }
    .galeri__popup--2 {
      margin-top: -1rem;
      /*top: 10rem;*/ }
      @media only screen and (max-width: 56.25em) {
        .galeri__popup--2 {
          margin-top: -26rem; } }

.galeri-404 {
  position: relative; }
  .galeri-404__gorsel {
    width: 100%;
    height: 55rem;
    outline-offset: 2rem;
    z-index: 10;
    transition: all .2s; }
    @media only screen and (max-width: 56.25em) {
      .galeri-404__gorsel {
        float: left;
        height: 100%;
        box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.2); } }
    .galeri-404__gorsel--3 {
      left: 20%;
      margin-top: -8rem;
      /*top: 10rem;*/ }
      @media only screen and (max-width: 56.25em) {
        .galeri-404__gorsel--3 {
          margin-top: -1rem; } }
  .galeri-404:hover .galeri-404__gorsel:not(:hover) {
    transform: scale(0.95); }

.gallery {
  display: grid;
  grid-template-rows: repeat(1, 20vh);
  grid-template-columns: repeat(10, 20.3vh);
  background-color: #fff;
  border-radius: 10px;
  padding: 0.25em;
  cursor: zoom-in; }
  @media only screen and (max-width: 1024px) {
    .gallery {
      display: grid;
      grid-template-rows: repeat(1, 20vh);
      grid-template-columns: repeat(10, 7vh);
      background-color: #fff;
      border-radius: 10px;
      padding: 0.25em;
      cursor: zoom-in; } }
  @media only screen and (max-width: 40em) {
    .gallery {
      display: grid;
      grid-template-rows: repeat(2, 20vh);
      grid-template-columns: repeat(5, 20vw);
      background-color: #fff;
      border-radius: 10px;
      padding: 0.25em;
      cursor: zoom-in; } }
  @media only screen and (max-width: 56.25em) {
    .gallery {
      display: grid;
      grid-template-rows: none;
      grid-template-columns: none;
      background-color: #fff;
      border-radius: 10px;
      padding: 0.25em;
      cursor: zoom-in; } }

.gallery__img {
  object-fit: cover;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  position: relative;
  box-shadow: 0 0 0 #0000;
  opacity: 1; }
  .gallery__img:first-child {
    border-radius: 10px 10px 0 0; }
  .gallery__img:last-child {
    border-radius: 0 0 10px 10px; }
  @media (min-width: 40em) and (max-width: 59.99em) {
    .gallery__img:first-child {
      border-radius: 10px 0 0 0; }
    .gallery__img:nth-child(5) {
      border-radius: 0 10px 0 0; }
    .gallery__img:nth-child(6) {
      border-radius: 0 0 10px 0; }
    .gallery__img:last-child {
      border-radius: 0 0 0 10px; } }
  @media (min-width: 60em) {
    .gallery__img:first-child {
      border-radius: 10px 0 0 10px; }
    .gallery__img:last-child {
      border-radius: 0 10px 10px 0; } }
  .gallery__img:hover {
    opacity: 1;
    z-index: 1;
    box-shadow: 1em 1em 1em #0004;
    filter: sepia(0%) hue-rotate(0deg);
    border-radius: 5px;
    width: 300%;
    height: 300%;
    left: -100%;
    top: -100%; }
    @media (min-width: 40em) {
      .gallery__img:hover {
        width: 250%;
        height: 500%;
        left: -75%;
        top: -200%; } }
    @media (min-width: 40em) and (orientation: portrait) {
      .gallery__img:hover {
        width: 300%;
        height: 300%;
        left: -100%;
        top: -100%; } }
    @media (min-width: 60em) {
      .gallery__img:hover {
        width: 350%;
        height: 150%;
        left: -75%;
        top: -25%; }
        .gallery__img:hover ~ img {
          left: 175%; } }
    @media (min-width: 60em) and (orientation: landscape) {
      .gallery__img:hover {
        width: 300%;
        height: 300%;
        left: -75%;
        top: -100%; }
        .gallery__img:hover ~ img {
          left: 100%; } }

.grid-container {
  columns: 5 200px;
  column-gap: 1.5rem;
  width: 90%;
  margin: 0 auto; }
  .grid-container div {
    width: 150px;
    margin: 0 1.5rem 1.5rem 0;
    display: inline-block;
    width: 100%;
    border: solid 2px black;
    padding: 5px;
    box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.5);
    border-radius: 5px;
    transition: all .25s ease-in-out; }
    .grid-container div:hover img {
      filter: grayscale(0); }
    .grid-container div:hover {
      border-color: #373075; }
    .grid-container div img {
      width: 100%;
      border-radius: 5px;
      transition: all .25s ease-in-out; }

/* The Modal (background) */
.modal {
  display: none;
  position: fixed;
  z-index: 9999;
  padding-top: 100px;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: black; }

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  /*width: 90%;*/
  max-width: 1200px; }

/* The Close Button */
.close {
  color: white;
  position: absolute;
  top: 10px;
  right: 25px;
  font-size: 35px;
  font-weight: bold; }

.close:hover,
.close:focus {
  color: #999;
  text-decoration: none;
  cursor: pointer; }

/* Hide the slides by default */
.mySlides {
  display: none; }

/* Next & previous buttons */
.prev,
.next {
  cursor: pointer;
  position: absolute;
  top: 50%;
  width: auto;
  padding: 16px;
  margin-top: -50px;
  color: white;
  font-weight: bold;
  font-size: 20px;
  transition: 0.6s ease;
  border-radius: 0 3px 3px 0;
  user-select: none;
  -webkit-user-select: none; }
  @media only screen and (min-width: 700px) and (max-width: 812px) {
    .prev,
    .next {
      margin-top: -50px; } }
  @media only screen and (max-width: 40em) {
    .prev,
    .next {
      margin-top: -207px; } }

/* Position the "next button" to the right */
.next {
  right: 0;
  border-radius: 3px 0 0 3px; }

/* On hover, add a black background color with a little bit see-through */
.prev:hover,
.next:hover {
  background-color: rgba(0, 0, 0, 0.8); }

/* Number text (1/3 etc) */
.numbertext {
  color: #f2f2f2;
  font-size: 12px;
  padding: 8px 12px;
  position: absolute;
  top: 0; }

/* Caption text */
.caption-container {
  text-align: center;
  background-color: black;
  padding: 2px 16px;
  color: white; }

img.demo {
  opacity: 0.6; }

.active,
.demo:hover {
  opacity: 1; }

img.hover-shadow {
  transition: 0.3s; }

.hover-shadow:hover {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); }

img.ha-car {
  margin-top: -223px; }

img.hm-mask-car {
  margin-top: -143px; }

.hizmetler-kutu {
  background-color: #fff;
  padding: 2.5rem;
  text-align: center;
  font-size: 1.5rem;
  border-radius: 5.5rem 5.5rem;
  box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.15);
  transition: transform .3s; }
  @media only screen and (max-width: 56.25em) {
    .hizmetler-kutu {
      padding: 2rem; } }
  .hizmetler-kutu--icon {
    font-size: 6rem;
    margin-bottom: .5rem;
    display: inline-block;
    background-image: linear-gradient(to right, #33c4e7, #0c52aa);
    -webkit-background-clip: text;
    color: transparent; }
    @media only screen and (max-width: 56.25em) {
      .hizmetler-kutu--icon {
        margin-bottom: 0;
        font-size: 13rem; } }
  .hizmetler-kutu:hover {
    transform: translateY(-1.5rem) scale(1.03); }
  .hizmetler-kutu--yazi {
    text-align: center; }
    @media only screen and (max-width: 56.25em) {
      .hizmetler-kutu--yazi {
        font-size: 3.5rem; } }

.ozellik-kutu {
  /*background-color: $beyaz-renk;*/
  padding: 2.5rem;
  text-align: center;
  font-size: 1.5rem;
  /*box-shadow: 0 1.5rem 4rem rgba($siyah-renk, .15);*/
  transition: transform .3s;
  /*height: 330px;*/ }
  @media only screen and (max-width: 1180px) {
    .ozellik-kutu {
      text-align: center;
      align-items: center;
      clear: both;
      margin: auto; } }
  @media only screen and (max-width: 56.25em) {
    .ozellik-kutu {
      padding: 2rem; } }
  .ozellik-kutu--icon {
    font-size: 6rem;
    display: inline-block;
    background-image: linear-gradient(to right, #3b3664, #2e276a);
    -webkit-background-clip: text;
    color: transparent; }
    @media only screen and (max-width: 56.25em) {
      .ozellik-kutu--icon {
        margin-bottom: 0;
        font-size: 13rem; } }
  .ozellik-kutu:hover {
    /*transform: translateY(-1.5rem) scale(1.03);*/ }
  .ozellik-kutu--yazi {
    text-align: center;
    font-size: 1.4rem;
    font-weight: 400; }
    @media only screen and (max-width: 375px) {
      .ozellik-kutu--yazi {
        font-size: 3.3rem;
        width: 290px; } }
    @media only screen and (max-width: 56.25em) {
      .ozellik-kutu--yazi {
        font-size: 3.3rem; } }

img.haber-gorsel {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  border-image: linear-gradient(to right, #000000, #202020);
  border-image-slice: 1;
  border-radius: 214px 140px 201px 21px / 133px 94px 67px 19px; }
  @media only screen and (max-width: 1024px) {
    img.haber-gorsel {
      width: 100%; } }
  @media only screen and (max-width: 1180px) {
    img.haber-gorsel {
      width: 100%; } }

@media only screen and (max-width: 1024px) {
  img.team-gorsel {
    width: 100%; } }

.ozellik-kutu-siyah {
  background-color: #040404;
  color: #fff;
  padding: 2.5rem;
  text-align: center;
  font-size: 1.5rem;
  border-radius: .3rem;
  box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.15);
  transition: transform .3s; }
  @media only screen and (max-width: 56.25em) {
    .ozellik-kutu-siyah {
      padding: 2rem; } }
  .ozellik-kutu-siyah--icon {
    font-size: 6rem;
    margin-bottom: .5rem;
    display: inline-block;
    background-image: linear-gradient(to right, #ffffff, #cecece);
    -webkit-background-clip: text;
    color: transparent; }
    @media only screen and (max-width: 56.25em) {
      .ozellik-kutu-siyah--icon {
        margin-bottom: 0;
        font-size: 13rem; } }
  .ozellik-kutu-siyah:hover {
    transform: translateY(-1.5rem) scale(1.03); }
  .ozellik-kutu-siyah--yazi {
    text-align: center; }
    @media only screen and (max-width: 56.25em) {
      .ozellik-kutu-siyah--yazi {
        font-size: 2.3rem; } }

.ozellik-kutu-beyaz {
  background-color: #040404;
  color: #fff;
  padding: 2.5rem;
  text-align: center;
  font-size: 1.5rem;
  border-radius: .3rem;
  box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.15);
  transition: transform .3s; }
  @media only screen and (max-width: 56.25em) {
    .ozellik-kutu-beyaz {
      padding: 2rem; } }
  .ozellik-kutu-beyaz--icon {
    font-size: 6rem;
    margin-bottom: .5rem;
    display: inline-block;
    background-image: linear-gradient(to right, #ffffff, #cecece);
    -webkit-background-clip: text;
    color: transparent; }
    @media only screen and (max-width: 56.25em) {
      .ozellik-kutu-beyaz--icon {
        margin-bottom: 0;
        font-size: 13rem; } }
  .ozellik-kutu-beyaz:hover {
    transform: translateY(-1.5rem) scale(1.03); }
  .ozellik-kutu-beyaz--yazi {
    text-align: center;
    color: #fff; }
    @media only screen and (max-width: 56.25em) {
      .ozellik-kutu-beyaz--yazi {
        font-size: 3rem; } }

.post-kutu {
  padding: 2.5rem;
  text-align: center;
  font-size: 1.5rem;
  transition: transform .3s;
  border-radius: 5rem;
  -moz-border-radius: 50px;
  -webkit-border-radius: 500px;
  width: 450px;
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap; }
  @media only screen and (max-width: 1024px) {
    .post-kutu {
      width: 100%;
      display: flex;
      justify-content: center;
      flex-wrap: wrap; } }
  @media only screen and (max-width: 40em) {
    .post-kutu {
      width: 100%;
      display: flex;
      justify-content: center;
      flex-wrap: wrap; } }
  @media only screen and (max-width: 56.25em) {
    .post-kutu {
      width: 100%;
      display: flex;
      justify-content: center;
      flex-wrap: wrap; } }
  @media only screen and (max-width: 1180px) {
    .post-kutu {
      width: 100%;
      display: flex;
      justify-content: center;
      flex-wrap: wrap; } }
  .post-kutu--icon {
    font-size: 6rem;
    margin-bottom: -0.5rem;
    display: inline-block;
    background-image: linear-gradient(to right, #3b3664, #2e276a);
    -webkit-background-clip: text;
    color: transparent; }
    @media only screen and (max-width: 56.25em) {
      .post-kutu--icon {
        margin-bottom: 0;
        font-size: 13rem; } }
  .post-kutu--yazi {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 1rem; }
    @media only screen and (max-width: 56.25em) {
      .post-kutu--yazi {
        font-size: 3.5rem;
        text-align: center;
        padding: 2rem; } }

.yorum-kutu {
  padding: 2.5rem;
  text-align: center;
  font-size: 1.5rem;
  /*transition: transform .3s;*/
  border-radius: 5rem;
  -moz-border-radius: 50px;
  -webkit-border-radius: 500px;
  width: 450px;
  height: 453px; }
  @media only screen and (max-width: 1024px) {
    .yorum-kutu {
      width: auto;
      height: auto; } }
  @media only screen and (max-width: 40em) {
    .yorum-kutu {
      padding: 2rem;
      width: auto;
      height: auto;
      text-align: left; } }
  @media only screen and (max-width: 56.25em) {
    .yorum-kutu {
      padding: 2rem;
      width: auto;
      height: auto;
      text-align: left; } }
  .yorum-kutu--icon {
    font-size: 6rem;
    margin-bottom: -0.5rem;
    display: inline-block;
    background-image: linear-gradient(to right, #3b3664, #2e276a);
    -webkit-background-clip: text;
    color: transparent; }
    @media only screen and (max-width: 56.25em) {
      .yorum-kutu--icon {
        margin-bottom: 0;
        font-size: 13rem; } }
  .yorum-kutu:hover {
    /* transform: translateY(-1.5rem) scale(1.03);*/ }
  .yorum-kutu--yazi {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 1rem; }
    @media only screen and (max-width: 56.25em) {
      .yorum-kutu--yazi {
        font-size: 3.5rem;
        text-align: left;
        padding: 2rem; } }

.ozellik-kutu-iletisim {
  background-color: #fff;
  padding: 2.5rem;
  text-align: center;
  font-size: 1.5rem;
  border-radius: 214px 140px 201px 21px / 133px 94px 67px 19px;
  box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.15);
  /*transition: transform .3s;*/ }
  @media only screen and (max-width: 56.25em) {
    .ozellik-kutu-iletisim {
      padding: 2rem; } }
  .ozellik-kutu-iletisim--icon {
    font-size: 6rem;
    margin-bottom: -2rem;
    display: inline-block;
    background-image: linear-gradient(to right, #2e276a, #373075);
    -webkit-background-clip: text;
    color: transparent; }
    @media only screen and (max-width: 56.25em) {
      .ozellik-kutu-iletisim--icon {
        margin-bottom: 0;
        font-size: 13rem; } }
  .ozellik-kutu-iletisim:hover {
    /*transform: translateY(-1.5rem) scale(1.03);*/ }
  .ozellik-kutu-iletisim--yazi {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 400;
    color: #797979; }
    @media only screen and (max-width: 56.25em) {
      .ozellik-kutu-iletisim--yazi {
        font-size: 3.5rem; } }
  .ozellik-kutu-iletisim--yazi-iletisim {
    text-align: center;
    font-size: 1.6rem;
    font-weight: 400;
    color: #373075; }
    @media only screen and (max-width: 56.25em) {
      .ozellik-kutu-iletisim--yazi-iletisim {
        font-size: 3.5rem; } }

.baslik-4 {
  font-size: 1.8rem;
  text-transform: uppercase;
  font-weight: 600;
  color: #0f0f0f; }
  @media only screen and (max-width: 1024px) {
    .baslik-4 {
      width: auto; } }
  @media only screen and (max-width: 56.25em) {
    .baslik-4 {
      padding: 2rem;
      font-size: 5rem; } }
  .baslik-4--icon {
    font-size: 6rem;
    display: inline-block;
    background-image: linear-gradient(to right, #3b3664, #2e276a);
    -webkit-background-clip: text;
    color: transparent; }
    @media only screen and (max-width: 56.25em) {
      .baslik-4--icon {
        margin-bottom: 0;
        font-size: 13rem; } }
  .baslik-4:hover {
    /*transform: translateY(-1.5rem) scale(1.03);*/ }
  .baslik-4--yazi {
    text-align: center;
    font-size: 1.3rem;
    font-weight: 400; }
    @media only screen and (max-width: 375px) {
      .baslik-4--yazi {
        font-size: 3.1rem;
        width: 290px; } }
    @media only screen and (max-width: 56.25em) {
      .baslik-4--yazi {
        font-size: 3.3rem; } }

.baslik-41 {
  font-size: 1.8rem;
  text-transform: uppercase;
  font-weight: 600;
  color: #0f0f0f; }
  @media only screen and (max-width: 1024px) {
    .baslik-41 {
      width: auto; } }
  @media only screen and (max-width: 56.25em) {
    .baslik-41 {
      padding: 2rem;
      font-size: 5rem;
      margin-top: 8rem; } }
  .baslik-41--icon {
    font-size: 6rem;
    display: inline-block;
    background-image: linear-gradient(to right, #3b3664, #2e276a);
    -webkit-background-clip: text;
    color: transparent; }
    @media only screen and (max-width: 56.25em) {
      .baslik-41--icon {
        margin-bottom: 0;
        font-size: 13rem; } }
  .baslik-41:hover {
    /*transform: translateY(-1.5rem) scale(1.03);*/ }
  .baslik-41--yazi {
    text-align: center;
    font-size: 1.3rem;
    font-weight: 400; }
    @media only screen and (max-width: 375px) {
      .baslik-41--yazi {
        font-size: 3.1rem;
        width: 290px; } }
    @media only screen and (max-width: 56.25em) {
      .baslik-41--yazi {
        font-size: 3.3rem; } }

.ozellik-kutu-yorumlar {
  background-color: #fff;
  padding: 2.5rem;
  text-align: center;
  font-size: 1.5rem;
  box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.15);
  transition: transform .3s;
  width: 385px;
  /*height: 330px;*/ }
  @media only screen and (max-width: 1024px) {
    .ozellik-kutu-yorumlar {
      width: auto; } }
  @media only screen and (max-width: 56.25em) {
    .ozellik-kutu-yorumlar {
      padding: 2rem; } }
  .ozellik-kutu-yorumlar--icon {
    font-size: 6rem;
    display: inline-block;
    background-image: linear-gradient(to right, #3b3664, #2e276a);
    -webkit-background-clip: text;
    color: transparent; }
    @media only screen and (max-width: 56.25em) {
      .ozellik-kutu-yorumlar--icon {
        margin-bottom: 0;
        font-size: 13rem; } }
  .ozellik-kutu-yorumlar:hover {
    /*transform: translateY(-1.5rem) scale(1.03);*/ }
  .ozellik-kutu-yorumlar--yazi {
    text-align: center;
    font-size: 1.3rem;
    font-weight: 400; }
    @media only screen and (max-width: 375px) {
      .ozellik-kutu-yorumlar--yazi {
        font-size: 3.1rem;
        width: 290px; } }
    @media only screen and (max-width: 56.25em) {
      .ozellik-kutu-yorumlar--yazi {
        font-size: 3.3rem; } }

.services-kutu1 {
  /*background-color: $beyaz-renk;*/
  padding: 2.5rem;
  text-align: left;
  font-size: 1.5rem;
  /*box-shadow: 0 1.5rem 4rem rgba($siyah-renk, .15);*/
  transition: transform .3s;
  border-right: 1px solid #e2e2e2;
  /*height: 330px;*/ }
  @media only screen and (max-width: 1180px) {
    .services-kutu1 {
      text-align: center;
      align-items: center;
      clear: both;
      margin: auto; } }
  @media only screen and (max-width: 56.25em) {
    .services-kutu1 {
      padding: 2rem; } }
  .services-kutu1--icon {
    font-size: 6rem;
    display: inline-block;
    background-image: linear-gradient(to right, #3b3664, #2e276a);
    -webkit-background-clip: text;
    color: transparent; }
    @media only screen and (max-width: 56.25em) {
      .services-kutu1--icon {
        margin-bottom: 0;
        font-size: 13rem; } }
  .services-kutu1:hover {
    /*transform: translateY(-1.5rem) scale(1.03);*/ }
  .services-kutu1--yazi {
    text-align: left;
    font-size: 1.4rem;
    font-weight: 400;
    color: #ffffff; }
    @media only screen and (max-width: 375px) {
      .services-kutu1--yazi {
        font-size: 3.3rem;
        width: 290px; } }
    @media only screen and (max-width: 56.25em) {
      .services-kutu1--yazi {
        font-size: 3.3rem;
        text-align: center; } }

.services-kutu2 {
  /*background-color: $beyaz-renk;*/
  padding: 2.5rem;
  text-align: center;
  font-size: 1.5rem;
  /*box-shadow: 0 1.5rem 4rem rgba($siyah-renk, .15);*/
  transition: transform .3s;
  /*border-right: 1px solid #e2e2e2;*/
  /*height: 330px;*/ }
  @media only screen and (max-width: 1180px) {
    .services-kutu2 {
      text-align: center;
      align-items: center;
      clear: both;
      margin: auto; } }
  @media only screen and (max-width: 56.25em) {
    .services-kutu2 {
      padding: 2rem; } }
  .services-kutu2--icon {
    font-size: 6rem;
    display: inline-block;
    background-image: linear-gradient(to right, #3b3664, #2e276a);
    -webkit-background-clip: text;
    color: transparent; }
    @media only screen and (max-width: 56.25em) {
      .services-kutu2--icon {
        margin-bottom: 0;
        font-size: 13rem; } }
  .services-kutu2:hover {
    /*transform: translateY(-1.5rem) scale(1.03);*/ }
  .services-kutu2--yazi {
    text-align: center;
    font-size: 1.4rem;
    font-weight: 400;
    color: #ffffff; }
    @media only screen and (max-width: 375px) {
      .services-kutu2--yazi {
        font-size: 3.3rem;
        width: 290px; } }
    @media only screen and (max-width: 56.25em) {
      .services-kutu2--yazi {
        font-size: 3.3rem; } }
  .services-kutu2--yazi1 {
    text-align: center;
    font-size: 1.4rem;
    font-weight: 400;
    color: #000000; }
    @media only screen and (max-width: 375px) {
      .services-kutu2--yazi1 {
        font-size: 3.3rem;
        width: 290px; } }
    @media only screen and (max-width: 56.25em) {
      .services-kutu2--yazi1 {
        font-size: 3.3rem; } }

.services-kutu3 {
  /*background-color: $beyaz-renk;*/
  padding: 2.5rem;
  text-align: left;
  font-size: 1.5rem;
  /*box-shadow: 0 1.5rem 4rem rgba($siyah-renk, .15);*/
  transition: transform .3s;
  /*height: 330px;*/ }
  @media only screen and (max-width: 1180px) {
    .services-kutu3 {
      text-align: center;
      align-items: center;
      clear: both;
      margin: auto; } }
  @media only screen and (max-width: 56.25em) {
    .services-kutu3 {
      padding: 2rem; } }
  .services-kutu3--icon {
    font-size: 6rem;
    display: inline-block;
    background-image: linear-gradient(to right, #3b3664, #2e276a);
    -webkit-background-clip: text;
    color: transparent; }
    @media only screen and (max-width: 56.25em) {
      .services-kutu3--icon {
        margin-bottom: 0;
        font-size: 13rem; } }
  .services-kutu3:hover {
    /*transform: translateY(-1.5rem) scale(1.03);*/ }
  .services-kutu3--yazi {
    text-align: left;
    font-size: 1.4rem;
    font-weight: 400;
    color: #ffffff; }
    @media only screen and (max-width: 375px) {
      .services-kutu3--yazi {
        font-size: 3.3rem;
        width: 290px; } }
    @media only screen and (max-width: 56.25em) {
      .services-kutu3--yazi {
        font-size: 3.3rem; } }

.gtco-testimonials {
  position: relative;
  margin-top: 30px; }

@media (max-width: 767px) {
  .gtco-testimonials {
    margin-top: 20px; } }
.gtco-testimonials h2 {
  font-size: 30px;
  text-align: center;
  color: #333333;
  margin-bottom: 50px; }

.gtco-testimonials .owl-stage-outer {
  padding: 30px 0; }

.gtco-testimonials .owl-nav {
  display: none; }

.gtco-testimonials .owl-dots {
  text-align: center; }

.gtco-testimonials .owl-dots span {
  position: relative;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  display: block;
  background: #fff;
  border: 2px solid #373075;
  margin: 0 5px; }

.gtco-testimonials .owl-dots .active {
  box-shadow: none; }

.gtco-testimonials .owl-dots .active span {
  background: #373075;
  box-shadow: none;
  height: 12px;
  width: 12px;
  margin-bottom: -1px; }

.gtco-testimonials .card {
  background: #fff;
  box-shadow: 0 8px 30px -7px #c9dff0;
  margin: 0 20px;
  padding: 0 10px;
  border-radius: 20px;
  border: 0; }

.gtco-testimonials .card .card-img-top {
  max-width: 100px;
  border-radius: 50%;
  margin: 15px auto 0;
  box-shadow: 0 8px 20px -4px #95abbb;
  width: 100px;
  height: 100px; }

.gtco-testimonials .card h5 {
  color: #373075;
  font-size: 21px;
  line-height: 1.3; }

.gtco-testimonials .card h5 span {
  font-size: 18px;
  color: #666666; }

.gtco-testimonials .card p {
  font-size: 18px;
  color: #555;
  padding-bottom: 15px; }

.gtco-testimonials .active {
  opacity: 0.5;
  transition: all 0.3s; }

.gtco-testimonials .center {
  opacity: 1; }

.gtco-testimonials .center h5 {
  font-size: 24px; }

.gtco-testimonials .center h5 span {
  font-size: 20px; }

.gtco-testimonials .center .card-img-top {
  max-width: 100%;
  height: 120px;
  width: 120px; }

.owl-carousel .owl-nav button.owl-next, .owl-carousel .owl-nav button.owl-prev, .owl-carousel button.owl-dot {
  outline: 0; }

.testimonials-section {
  /*background: #fff;*/
  height: 600px;
  position: relative;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
  -ms-flex-align: end;
  align-items: flex-end;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  z-index: 0; }

.slider__nav {
  width: 68px;
  height: 10px;
  margin: 102px 8px;
  z-index: 10;
  outline: 5px solid #202020;
  outline-offset: -8px;
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden; }

.slider__nav:checked {
  -webkit-animation: check 0.4s linear forwards;
  animation: check 0.4s linear forwards; }

.slider__nav:checked:nth-of-type(1) ~ .slider__inner {
  left: 0%; }

.slider__nav:checked:nth-of-type(2) ~ .slider__inner {
  left: -100%; }

.slider__nav:checked:nth-of-type(3) ~ .slider__inner {
  left: -200%; }

.slider__nav:checked:nth-of-type(4) ~ .slider__inner {
  left: -300%; }

.slider__nav:checked:nth-of-type(5) ~ .slider__inner {
  left: -400%; }

.slider__inner {
  position: absolute;
  top: 80px;
  left: 0;
  width: 500%;
  height: auto;
  -webkit-transition: left 0.4s;
  transition: left 0.4s;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap; }

.slider__contents {
  height: 100%;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-flex-flow: column nowrap;
  -ms-flex-flow: column nowrap;
  flex-flow: column nowrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center; }

.slider__caption {
  font-size: 14px;
  color: #111;
  opacity: .5;
  font-weight: 600; }

.slider__txt {
  font-size: 1.4rem;
  font-weight: 400;
  line-height: 1.7;
  color: #797979;
  margin-top: -20px;
  margin-bottom: 20px;
  max-width: 750px;
  text-align: center; }
  @media only screen and (max-width: 56.25em) {
    .slider__txt {
      font-size: 2.5rem; } }

quote {
  font-family: 'Arial';
  font-weight: bold;
  font-size: 100px;
  color: #be1e2d;
  margin-bottom: 0; }

@-webkit-keyframes check {
  50% {
    outline-color: #656768; }
  100% {
    outline-color: #656769; } }
@keyframes check {
  50% {
    outline-color: #56595a; }
  100% {
    outline-color: #555657; } }
img.timg {
  padding: 5rem; }

.paketler {
  perspective: 150rem;
  -moz-perspective: 150rem;
  position: relative;
  height: 52rem; }
  .paketler__on {
    background-color: #2e276a;
    height: 52rem;
    transition: all .8s ease;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    backface-visibility: hidden;
    border-radius: .3rem;
    overflow: hidden;
    box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.15); }
    .paketler__on--onyazi {
      background-color: #fff; }
    .paketler__on--arkayazi {
      transform: rotateY(180deg); }
      .paketler__on--arkayazi-1 {
        background-image: linear-gradient(to right bottom, #000, #000); }
      .paketler__on--arkayazi-2 {
        background-image: linear-gradient(to right bottom, #000, #000); }
      .paketler__on--arkayazi-3 {
        background-image: linear-gradient(to right bottom, #000, #000); }
  .paketler:hover .paketler__on--onyazi {
    transform: rotateY(-180deg); }
  .paketler:hover .paketler__on--arkayazi {
    transform: rotateY(0); }
  .paketler__gorsel {
    background-size: cover;
    height: 23rem;
    background-blend-mode: color;
    -webkit-clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
    clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
    border-top-left-radius: .3rem;
    border-top-right-radius: .3rem; }
    @media only screen and (max-width: 56.25em) {
      .paketler__gorsel {
        height: 44rem; } }
    .paketler__gorsel--1 {
      background-image: linear-gradient(to right bottom, #0e0e0e, #1c1a1a), url(../img/pack1.jpg); }
    .paketler__gorsel--2 {
      background-image: linear-gradient(to right bottom, #0e0e0e, #1c1a1a), url(../img/pack2.jpg); }
    .paketler__gorsel--3 {
      background-image: linear-gradient(to right bottom, #0e0e0e, #1c1a1a), url(../img/pack3.jpg); }
  .paketler__baslik {
    font-size: 2.2rem;
    font-weight: 300;
    color: #fff;
    text-align: right;
    /*text-transform: uppercase;*/
    position: absolute;
    top: 15rem;
    margin-left: 8rem;
    width: 75%; }
  .paketler__baslik-span {
    padding: 1rem 1.5rem;
    -webkit-box-decoration-break: clone;
    box-decoration-break: clone; }
    .paketler__baslik-span--1 {
      background-image: linear-gradient(to right bottom, rgba(36, 36, 36, 0.85), rgba(22, 22, 22, 0.85)); }
    .paketler__baslik-span--2 {
      background-image: linear-gradient(to right bottom, rgba(46, 39, 106, 0.85), rgba(46, 39, 106, 0.85)); }
    .paketler__baslik-span--3 {
      background-image: linear-gradient(to right bottom, rgba(46, 39, 106, 0.85), rgba(46, 39, 106, 0.85)); }
  .paketler__icerik {
    padding: 3rem; }
    .paketler__icerik ul {
      list-style: none;
      width: 80%;
      margin: 0 auto; }
      .paketler__icerik ul li {
        text-align: center;
        font-size: 1.5rem;
        padding: 1rem; }
        .paketler__icerik ul li:not(:last-child) {
          border-bottom: 0.1rem solid #eee; }
        @media only screen and (max-width: 56.25em) {
          .paketler__icerik ul li {
            font-size: 3.5rem; } }
  .paketler__pr {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    text-align: center; }
  .paketler__pr-kutu {
    text-align: center;
    margin-bottom: 8rem;
    color: #fff; }
  .paketler__pr-yazi {
    font-size: 1.8rem;
    text-transform: uppercase;
    text-align: center;
    font-weight: 600; }
    @media only screen and (max-width: 56.25em) {
      .paketler__pr-yazi {
        font-size: 4.4rem; } }
  .paketler__pr-degeri {
    font-size: 6rem;
    font-weight: 100;
    text-align: center; }
    @media only screen and (max-width: 56.25em) {
      .paketler__pr-degeri {
        font-size: 11rem; } }
  @media only screen and (max-width: 56.25em), only screen and (hover: none) {
    .paketler {
      height: auto;
      border-radius: .3rem;
      background-color: #fff;
      box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.15); }
      .paketler__on {
        height: auto;
        position: relative;
        box-shadow: none; }
        .paketler__on--arkayazi {
          transform: rotateY(0);
          clip-path: polygon(0 15%, 100% 0, 100% 100%, 0% 100%); }
      .paketler:hover .paketler__on--onyazi {
        transform: rotateY(0); }
      .paketler__icerik {
        padding: 1rem 3rem; }
      .paketler__pr {
        position: relative;
        top: 0;
        left: 0;
        transform: translate(0);
        width: 100%;
        padding: 7rem 4rem 4rem 4rem; }
      .paketler__pr-kutu {
        margin-bottom: 3rem; }
      .paketler__pr-degeri {
        font-size: 4rem; } }
    @media only screen and (max-width: 56.25em) and (max-width: 56.25em), only screen and (hover: none) and (max-width: 56.25em) {
      .paketler__pr-degeri {
        font-size: 11rem; } }

.referans {
  height: 12.5rem;
  margin-left: 4rem;
  filter: alpha(opacity=40);
  opacity: 0.4;
  transition: all .2s ease-out; }
  @media only screen and (max-width: 56.25em) {
    .referans {
      height: 23.5rem; } }
  .referans:hover {
    height: 12.5rem;
    margin-left: 4rem;
    opacity: 1;
    transform: translateY(-0.3rem); }
    @media only screen and (max-width: 56.25em) {
      .referans:hover {
        height: 23.5rem; } }

.popup {
  height: 100vh;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all .3s; }
  .popup:target {
    opacity: 1;
    visibility: visible; }
  .popup__kapat:link, .popup__kapat:visited {
    color: #000;
    position: absolute;
    top: 0rem;
    right: 1.5rem;
    font-size: 4rem;
    display: inline-block;
    transition: all .2s; }
    @media only screen and (max-width: 56.25em) {
      .popup__kapat:link, .popup__kapat:visited {
        font-size: 7rem; } }
  .popup__kapat:hover {
    color: #2e276a; }
  .popup__content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30%;
    height: 50rem;
    background-color: #fff;
    box-shadow: 0 2rem 4rem rgba(0, 0, 0, 0.2); }
    @media only screen and (max-width: 56.25em) {
      .popup__content {
        width: 80%;
        height: 80rem; } }
    @media only screen and (max-width: 40em) {
      .popup__content {
        width: 80%;
        height: 80rem; } }

/* style member component */
.list-members {
  background-image: linear-gradient(to right, #000000, #0c0c0c);
  width: 80%;
  margin: 4% auto;
  display: flex;
  flex-wrap: wrap-reverse; }

.member {
  flex-basis: 50%;
  display: flex;
  align-items: center;
  justify-content: space-between; }

.member-image {
  width: 50%;
  height: 100%;
  cursor: pointer;
  overflow: hidden;
  position: relative; }

.member-image img {
  width: 100%;
  height: 100%;
  transition: 1s; }

.member-image:hover img {
  transform: scale(1.1); }

.member-info {
  width: 50%;
  text-align: center; }

.member-info p {
  margin: 20px 0;
  text-align: center; }

/* style social link */
.social-link .fab {
  width: 35px;
  height: 35px;
  line-height: 35px;
  border: 1px solid #fff;
  margin: 0 7px;
  cursor: pointer;
  transition: transform .5s;
  background: #fff;
  color: #020202; }

.social-link .fab:hover {
  background: #020202;
  color: #fff;
  transform: translateY(-7px); }

/* Membuat segitiga */
.member-image::after {
  content: '';
  border-top: 20px solid transparent;
  border-bottom: 20px solid transparent;
  border-right: 15px solid  #020202;
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%); }

/* Merubah posisi member-image dengan member-info */
@media screen and (min-width: 771px) {
  .member:nth-child(4n+3) .member-info,
  .member:nth-child(4n+4) .member-info {
    order: 1; }

  .member:nth-child(4n+3) .member-image,
  .member:nth-child(4n+4) .member-image {
    order: 2; }

  /* Merubah posisi sigitiga pada baris genap */
  .member:nth-child(4n+3) .member-image::after,
  .member:nth-child(4n+4) .member-image::after {
    left: 0;
    right: auto;
    transform: translateY(-50%) rotateZ(180deg); } }
/* Mobile Styles */
@media screen and (max-width: 770px) {
  .list-members {
    width: 95%; }

  .member {
    flex-basis: 100%;
    font-size: 14px; }

  .social-link .fab {
    width: 30px;
    height: 30px;
    line-height: 30px; }

  .member:nth-child(even) .member-info {
    order: 1; }

  .member:nth-child(even) .member-image {
    order: 2; }

  /* Merubah posisi sigitiga elemen genap */
  .member:nth-child(even) .member-image::after {
    left: 0;
    right: auto;
    transform: translateY(-50%) rotateZ(180deg); } }
p.r1 {
  color: white; }

h3.r1 {
  color: white; }

.open-btn {
  position: fixed;
  top: 26px;
  right: 109px;
  background: #ffffff url(../img/icon_search-ss.png) no-repeat 15px center;
  background-size: 35px 35px;
  width: 72px;
  height: 65px;
  border-radius: 50%;
  text-align: center;
  cursor: pointer;
  z-index: 8; }
  @media only screen and (max-width: 40em) {
    .open-btn {
      top: 11px;
      right: 54px; } }
  @media (min-width: 641px) and (max-width: 900px) {
    .open-btn {
      top: 31px;
      right: 75px; } }

#search-wrap {
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  opacity: 0;
  transition: all 0.4s;
  width: 100%;
  height: 100vh; }

#search-wrap.panelactive {
  opacity: 1;
  z-index: 888;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center; }

#search-wrap .search-area {
  display: none; }

#search-wrap.panelactive .search-area {
  display: block;
  width: 80%;
  position: relative; }

#search-wrap form {
  position: relative;
  height: 66px; }

#search-wrap input {
  -webkit-appearance: none;
  outline: none;
  cursor: pointer;
  color: #666; }

#search-wrap input[type="text"] {
  width: 100%;
  padding: 20px;
  border: none;
  border-bottom: 2px solid #666;
  transition: all 0.5s;
  letter-spacing: 0.05em;
  font-size: 2rem;
  font-weight: 600; }

#search-wrap input[type="text"]:focus {
  background: #fff; }

#search-wrap input[type="submit"] {
  position: absolute;
  top: 0;
  right: 10px;
  background: url("../img/icon_search-s.png") no-repeat 15px center;
  background-size: 25px 25px;
  width: 60px;
  height: 60px;
  border: none; }

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 2;
  cursor: pointer;
  width: 60px;
  height: 60px;
  background: #8a8a99; }

.close-btn span {
  display: inline-block;
  position: absolute;
  left: 14px;
  height: 3px;
  border-radius: 2px;
  background-color: #fff; }

.close-btn span:nth-of-type(1) {
  top: 21px;
  left: 16px;
  transform: translateY(6px) rotate(-135deg);
  width: 50%; }

.close-btn span:nth-of-type(2) {
  top: 32px;
  left: 16px;
  transform: translateY(-6px) rotate(135deg);
  width: 50%; }

.footer {
  background-image: linear-gradient(to right bottom, #000000, #131414);
  padding: 1rem 0 1rem;
  font-size: 1.4rem;
  color: #fff;
  /*margin-top: -16rem;*/ }
  @media only screen and (max-width: 56.25em) {
    .footer {
      padding: 18rem 0;
      text-align: center; } }
  .footer__menu {
    border-top: 0.1rem solid #fff;
    padding-top: 2rem;
    display: inline-block;
    width: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 1.8rem; }
    @media only screen and (max-width: 56.25em) {
      .footer__menu {
        width: 100%;
        text-align: center; } }
  .footer__list {
    list-style: none; }
    @media only screen and (max-width: 56.25em) {
      .footer__list {
        font-size: 4rem; } }
  .footer__item {
    display: inline-block; }
    .footer__item:not(:last-child) {
      margin-right: 1.5rem; }
  .footer__link:link, .footer__link:visited {
    color: #fff;
    font-weight: 600;
    text-decoration: none;
    text-transform: uppercase;
    display: inline-block;
    transition: all .2s; }
  .footer__link:hover, .footer__link:active {
    color: #fff;
    transform: rotate(5deg) scale(1.3); }
  .footer__copyright {
    border-top: 0.1rem solid #fff;
    color: #fff;
    padding-top: 2rem;
    width: 100%; }
    @media only screen and (max-width: 56.25em) {
      .footer__copyright {
        width: 100%;
        float: none;
        text-align: center;
        font-size: 2.7rem; } }
  .footer__sosyal {
    border-top: 0.1rem solid #fff;
    padding-top: 2rem;
    width: 48%;
    text-align: center; }
    @media only screen and (max-width: 56.25em) {
      .footer__sosyal {
        width: 100%;
        float: none;
        text-align: center; } }
  .footer__sosyallink:link, .footer__sosyallink:visited {
    color: #fff;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all .2s; }
    @media only screen and (max-width: 56.25em) {
      .footer__sosyallink:link, .footer__sosyallink:visited {
        font-size: 5rem; } }
  .footer__sosyallink:hover, .footer__sosyallink:active {
    color: #fff;
    transform: translateY(-0.3rem); }

.footer-404 {
  background-image: linear-gradient(to right bottom, #33c4e7, #259cb9);
  padding: 5rem 0 1rem;
  font-size: 1.4rem;
  color: #fff;
  margin-top: -10rem; }
  @media only screen and (max-width: 56.25em) {
    .footer-404 {
      padding: 18rem 0;
      text-align: center; } }

.ozel {
  margin-left: 6rem; }
  .ozel-copyright {
    margin-left: 16.2rem;
    margin-top: -2rem; }
    @media only screen and (max-width: 40em) {
      .ozel-copyright {
        margin-left: 1rem; } }
    @media only screen and (max-width: 56.25em) {
      .ozel-copyright {
        margin-left: 1rem; } }
  @media only screen and (max-width: 40em) {
    .ozel {
      margin-left: 1rem; } }
  @media only screen and (max-width: 56.25em) {
    .ozel {
      margin-left: 1rem; } }

.iletisim-icon {
  background-image: linear-gradient(to right, #fdfdfd, #e0dfdf);
  -webkit-background-clip: text;
  color: transparent;
  font-size: 5.5rem;
  transition: all .2s ease-in-out; }
  @media only screen and (max-width: 40em) {
    .iletisim-icon {
      font-size: 13.5rem; } }
  @media only screen and (max-width: 56.25em) {
    .iletisim-icon {
      font-size: 13.5rem; } }

.ozel-iletisim {
  margin-left: 16rem;
  margin-top: -3rem; }
  @media only screen and (max-width: 40em) {
    .ozel-iletisim {
      margin-left: 5rem;
      margin-top: 1rem;
      padding: .5rem 2rem .2rem 1.5rem; } }
  @media only screen and (max-width: 56.25em) {
    .ozel-iletisim {
      margin-left: 5rem;
      margin-top: 1rem;
      padding: .5rem 2rem .2rem 1.5rem; } }
  .ozel-iletisim__yazi {
    color: #e0e0e0;
    font-weight: 600;
    font-family: sans-serif;
    font-size: 4.2rem;
    /*margin-left: 6rem;*/
    margin-top: -3.5rem; }
    @media only screen and (max-width: 56.25em) {
      .ozel-iletisim__yazi {
        font-size: 5.2rem;
        margin-left: 0rem; } }
    @media only screen and (max-width: 40em) {
      .ozel-iletisim__yazi {
        font-size: 5.2rem;
        margin-left: 0rem; } }

p.footer-address {
  font-size: 1.4rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 3rem; }
  @media only screen and (max-width: 56.25em) {
    p.footer-address {
      width: 100%;
      text-align: center;
      font-size: 4rem; } }

p.footer-support {
  font-size: 1.4rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 3rem; }
  @media only screen and (max-width: 56.25em) {
    p.footer-support {
      width: 100%;
      text-align: center;
      font-size: 4rem; } }

/* CUSTOM CONTAINER */
@media (min-width: 1170px) {
  .container {
    max-width: 1100px; } }
@media (min-width: 1280px) {
  .container {
    max-width: 1260px; } }
/* CUSTOM CLASSES */
.overflow {
  overflow: hidden; }

.no-gutters {
  padding: 0;
  margin: 0; }

/* SPACING */
.no-spacing {
  margin: 0 !important;
  padding: 0 !important; }

.no-top-spacing {
  margin-top: 0 !important;
  padding-top: 0 !important; }

.no-bottom-spacing {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important; }

/* HAMBURGER MENU */
.hamburger-menu {
  width: 30px;
  height: 20px;
  position: relative;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  transform: rotate(0deg);
  transition-duration: 500ms;
  -webkit-transition-duration: 500ms;
  transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
  cursor: pointer; }

.hamburger-menu span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: #fff;
  opacity: 1;
  left: 0;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  transform: rotate(0deg);
  -webkit-transition: .25s ease-in-out;
  -moz-transition: .25s ease-in-out;
  -o-transition: .25s ease-in-out;
  transition: .25s ease-in-out; }

.hamburger-menu span:nth-child(1) {
  top: 0px;
  width: 100%; }

.hamburger-menu span:nth-child(2) {
  top: 9px;
  width: 22px; }

.hamburger-menu span:nth-child(3) {
  top: 18px;
  width: 100%; }

.hamburger-menu:hover span {
  width: 100% !important; }

.hamburger-menu.open span {
  width: 20px !important; }

.hamburger-menu.open span:nth-child(1) {
  top: 9px;
  right: 0;
  -webkit-transform: rotate(135deg);
  -moz-transform: rotate(135deg);
  -o-transform: rotate(135deg);
  transform: rotate(135deg);
  width: 28px !important; }

.hamburger-menu.open span:nth-child(2) {
  opacity: 0;
  left: -20px; }

.hamburger-menu.open span:nth-child(3) {
  top: 9px;
  right: 0;
  -webkit-transform: rotate(-135deg);
  -moz-transform: rotate(-135deg);
  -o-transform: rotate(-135deg);
  transform: rotate(-135deg);
  width: 28px !important; }

/* TOPBAR */
.topbar {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  background: #0d0d0d;
  padding: 10px 0;
  color: #fff; }

.topbar div {
  display: inline-block;
  font-size: 16px;
  font-family: 'Barlow', sans-serif; }

.topbar div b {
  font-weight: 500;
  display: inline-block;
  margin-right: 6px;
  opacity: 0.5; }

.topbar div a {
  display: inline-block;
  color: #fff; }

/* NAVBAR */
.navbar {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2; }

.navbar .logo {
  margin-right: auto;
  padding: 30px 0;
  padding-right: 30px; }

.navbar .logo a {
  display: inline-block; }

.navbar .logo a img {
  height: 50px; }

.navbar .site-menu {
  margin: 0 auto; }

.navbar .site-menu ul {
  display: flex;
  flex-wrap: wrap;
  margin: 0;
  padding: 0; }

.navbar .site-menu ul li {
  display: inline-block;
  margin: 0;
  padding: 0 15px;
  list-style: none;
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out; }

.navbar .site-menu ul li a {
  color: #fff;
  font-weight: 500; }

.navbar .site-menu ul li a:hover {
  text-decoration: none; }

.navbar .hamburger-menu {
  margin-left: auto; }

.navbar .navbar-button {
  margin-left: 30px; }

.navbar .navbar-button a {
  height: 70px;
  line-height: 70px;
  display: inline-block;
  background: #25aae2;
  color: #fff;
  padding: 0 50px;
  position: relative; }

.navbar .navbar-button a:before {
  content: "";
  width: 0;
  height: 100%;
  background: #000;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.05;
  transition-duration: 1s;
  -webkit-transition-duration: 1s;
  transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1); }

.navbar .navbar-button a:hover {
  text-decoration: none;
  color: #fff; }

.navbar .navbar-button a:hover:before {
  width: 100%; }

@font-face {
  font-family: 'Mohave';
  src: url("../fonts/Mohave-Bold.eot");
  src: url("../fonts/Mohave-Bold.eot?#iefix") format("embedded-opentype"), url("../fonts/Mohave-Bold.woff2") format("woff2"), url("../fonts/Mohave-Bold.woff") format("woff"), url("../fonts/Mohave-Bold.ttf") format("truetype"), url("../fonts/Mohave-Bold.svg#Mohave-Bold") format("svg");
  font-weight: 700;
  font-style: normal; }
@font-face {
  font-family: 'Mohave';
  src: url("../fonts/Mohave-Regular.eot");
  src: url("../fonts/Mohave-Regular.eot?#iefix") format("embedded-opentype"), url("../fonts/Mohave-Regular.woff2") format("woff2"), url("../fonts/Mohave-Regular.woff") format("woff"), url("../fonts/Mohave-Regular.ttf") format("truetype"), url("../fonts/Mohave-Regular.svg#Mohave-Regular") format("svg");
  font-weight: 400;
  font-style: normal; }
@font-face {
  font-family: 'Mohave';
  src: url("../fonts/Mohave-Medium.eot");
  src: url("../fonts/Mohave-Medium.eot?#iefix") format("embedded-opentype"), url("../fonts/Mohave-Medium.woff2") format("woff2"), url("../fonts/Mohave-Medium.woff") format("woff"), url("../fonts/Mohave-Medium.ttf") format("truetype"), url("../fonts/Mohave-Medium.svg#Mohave-Medium") format("svg");
  font-weight: 500;
  font-style: normal; }
* {
  outline: none !important; }

body {
  margin: 0;
  padding: 0;
  font-family: "Mohave";
  font-size: 18px;
  color: #0d0d0d;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased; }

/* LINKS */
a {
  color: #0d0d0d;
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out; }

a:hover {
  text-decoration: underline;
  color: #0d0d0d; }

/* HTML TAGS */
img {
  max-width: 100%; }

p {
  font-family: 'Barlow', sans-serif; }

/* FORM ELEMENTS */
input[type="text"] {
  width: 420px;
  max-width: 100%;
  height: 70px;
  padding: 0 30px;
  border: 1px solid #cecece; }

input[type="email"] {
  width: 420px;
  max-width: 100%;
  height: 70px;
  padding: 0 30px;
  border: 1px solid #cecece; }

input[type="search"] {
  width: 420px;
  max-width: 100%;
  height: 70px;
  padding: 0 30px;
  border: 1px solid #cecece; }

input[type="password"] {
  width: 420px;
  max-width: 100%;
  height: 70px;
  padding: 0 30px;
  border: 1px solid #cecece; }

input[type="radio"] {
  width: 18px;
  height: 18px;
  display: inline-block;
  margin-right: 4px;
  transform: translateY(3px);
  appearance: none;
  background: #ededed;
  border-radius: 50%; }

input[type="radio"]:checked {
  border: 6px solid #0d0d0d; }

input[type="checkbox"] {
  width: 18px;
  height: 18px;
  display: inline-block;
  margin-right: 4px;
  transform: translateY(3px);
  appearance: none;
  background: #ededed; }

input[type="checkbox"]:checked {
  border: 6px solid #0d0d0d; }

textarea {
  width: 520px;
  max-width: 100%;
  height: 140px;
  padding: 30px;
  border: 1px solid #cecece; }

select {
  width: 420px;
  max-width: 100%;
  height: 70px;
  padding: 0 30px;
  border: 1px solid #cecece; }

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: linear-gradient(45deg, transparent 50%, gray 50%), linear-gradient(135deg, gray 50%, transparent 50%), linear-gradient(to right, #ccc, #ccc);
  background-position: calc(100% - 30px) 34px, calc(100% - 25px) 34px, calc(100% - 3.5em) 20px;
  background-size: 5px 5px, 5px 5px, 1px 40px;
  background-repeat: no-repeat; }

select:focus {
  background-image: linear-gradient(45deg, gray 50%, transparent 50%), linear-gradient(135deg, transparent 50%, gray 50%), linear-gradient(to right, #ccc, #ccc);
  background-position: calc(100% - 25px) 34px, calc(100% - 30px) 34px, calc(100% - 3.5em) 20px;
  background-size: 5px 5px, 5px 5px, 1px 40px;
  background-repeat: no-repeat;
  border-color: gray;
  outline: 0; }

select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #000; }

input[type="submit"] {
  height: 70px;
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  background: #0d0d0d;
  border: none;
  padding: 0 50px; }

button[type="submit"] {
  height: 70px;
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  background: #0d0d0d;
  border: none;
  padding: 0 50px; }

button[type="submit"] i {
  display: inline-block;
  margin-right: 8px;
  font-size: 18px;
  transform: translateY(2px); }

/* CUSTOM CONTAINER */
@media (min-width: 1170px) {
  .container {
    max-width: 1100px; } }
@media (min-width: 1280px) {
  .container {
    max-width: 1260px; } }
/* CUSTOM CLASSES */
.overflow {
  overflow: hidden; }

.no-gutters {
  padding: 0;
  margin: 0; }

/* SPACING */
.no-spacing {
  margin: 0 !important;
  padding: 0 !important; }

.no-top-spacing {
  margin-top: 0 !important;
  padding-top: 0 !important; }

.no-bottom-spacing {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important; }

/* HAMBURGER MENU */
.hamburger-menu {
  width: 30px;
  height: 20px;
  position: relative;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  transform: rotate(0deg);
  transition-duration: 500ms;
  -webkit-transition-duration: 500ms;
  transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
  cursor: pointer; }

.hamburger-menu span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: #fff;
  opacity: 1;
  left: 0;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  transform: rotate(0deg);
  -webkit-transition: .25s ease-in-out;
  -moz-transition: .25s ease-in-out;
  -o-transition: .25s ease-in-out;
  transition: .25s ease-in-out; }

.hamburger-menu span:nth-child(1) {
  top: 0px;
  width: 100%; }

.hamburger-menu span:nth-child(2) {
  top: 9px;
  width: 22px; }

.hamburger-menu span:nth-child(3) {
  top: 18px;
  width: 100%; }

.hamburger-menu:hover span {
  width: 100% !important; }

.hamburger-menu.open span {
  width: 20px !important; }

.hamburger-menu.open span:nth-child(1) {
  top: 9px;
  right: 0;
  -webkit-transform: rotate(135deg);
  -moz-transform: rotate(135deg);
  -o-transform: rotate(135deg);
  transform: rotate(135deg);
  width: 28px !important; }

.hamburger-menu.open span:nth-child(2) {
  opacity: 0;
  left: -20px; }

.hamburger-menu.open span:nth-child(3) {
  top: 9px;
  right: 0;
  -webkit-transform: rotate(-135deg);
  -moz-transform: rotate(-135deg);
  -o-transform: rotate(-135deg);
  transform: rotate(-135deg);
  width: 28px !important; }

/* PAGE HEADER */
.page-header {
  width: 100%;
  height: 700px;
  max-height: 100vh;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  position: relative;
  background-size: cover !important;
  padding-top: 100px; }

.page-header:after {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0; }

.page-header .container {
  position: relative;
  z-index: 1;
  color: #fff; }

.page-header .container h2 {
  width: 100%;
  display: block;
  font-size: 80px;
  margin-bottom: 0; }

.page-header .container p {
  width: 100%;
  display: block;
  margin-bottom: 50px;
  font-size: 20px; }

/* CONTENT SECTION */
.content-section {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 100px 0;
  position: relative; }

.content-section.left-white-spacing {
  position: relative; }

.content-section.left-white-spacing .container {
  position: relative;
  z-index: 1; }

.content-section.left-white-spacing:before {
  content: "";
  width: 30%;
  height: 100%;
  background: #fff;
  position: absolute;
  left: 0;
  top: 0; }

.content-section.bottom-dark-spacing {
  position: relative;
  padding-bottom: 0 !important; }

.content-section.bottom-dark-spacing .container {
  position: relative;
  z-index: 1; }

.content-section.bottom-dark-spacing:before {
  content: "";
  width: 30%;
  height: 100%;
  background: #fff;
  position: absolute;
  left: 0;
  top: 0; }

.content-section.bottom-dark-spacing:after {
  content: "";
  width: 100%;
  height: 140px;
  background: #0d0d0d;
  position: absolute;
  left: 0;
  bottom: 0; }

/* SECTION TITLE */
.section-title {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 50px;
  text-align: center; }

.section-title figure {
  width: 100%;
  display: block;
  margin-bottom: 15px; }

.section-title figure img {
  height: 40px; }

.section-title h6 {
  width: 100%;
  display: block; }

.section-title h2 {
  width: 100%;
  display: block;
  margin-bottom: 0;
  font-size: 70px;
  color: #25aae2; }

.section-title p {
  width: 100%;
  display: block;
  margin-bottom: 0;
  opacity: 0.7; }

/* TAB WRAPPER */
.tab-wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap; }

.tab-wrapper .tab-nav {
  width: 25%;
  margin: 0;
  padding: 0;
  padding-right: 40px;
  position: relative;
  z-index: 1; }

.tab-wrapper .tab-nav li {
  width: 100%;
  display: block;
  margin-bottom: 10px;
  list-style: none; }

.tab-wrapper .tab-nav li.active a {
  width: calc(100% + 60px);
  margin-right: -60px;
  background: #25aae2;
  color: #fff; }

.tab-wrapper .tab-nav li.active a:hover {
  background: #25aae2; }

.tab-wrapper .tab-nav li a {
  width: 100%;
  display: block;
  background: #f4f4f4;
  padding: 25px;
  font-weight: 700; }

.tab-wrapper .tab-nav li a:hover {
  background: #f0f0f0;
  text-decoration: none; }

.tab-wrapper .tab-item {
  width: 75%;
  display: none; }

.tab-wrapper .tab-item.active-item {
  display: flex; }

.tab-wrapper .tab-item .tab-inner {
  width: 100%;
  display: flex;
  position: relative;
  background: #0d0d0d; }

.tab-wrapper .tab-item .tab-inner ul {
  width: calc(350px - 100px);
  height: 40vw;
  overflow: auto;
  float: left;
  color: #fff;
  margin: 40px;
  margin-left: 60px;
  padding: 0; }

.tab-wrapper .tab-item .tab-inner ul li {
  width: 100%;
  display: block;
  margin-bottom: 20px;
  padding: 0;
  list-style: none; }

.tab-wrapper .tab-item .tab-inner ul li span {
  width: 100%;
  display: block;
  color: #25aae2; }

.tab-wrapper .tab-item .tab-inner ul li h6 {
  width: 100%;
  display: block; }

.tab-wrapper .tab-item .tab-inner ul li small {
  width: 100%;
  display: block;
  font-family: 'Barlow', sans-serif;
  opacity: 0.7; }

.tab-wrapper .tab-item .tab-inner figure {
  width: 100%;
  float: left;
  margin: 0; }

.tab-wrapper .tab-item .tab-inner figure img {
  width: 100%; }

/* COUNTER BOX */
.counter-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 40px;
  text-align: center;
  background: #fff;
  box-shadow: 0 0 40px rgba(0, 0, 0, 0.05); }

.counter-box figure {
  width: 100%;
  display: block;
  margin-bottom: 15px; }

.counter-box figure img {
  height: 90px; }

.counter-box .odometer {
  display: inline-block;
  line-height: 1;
  margin: 0 auto;
  font-size: 100px;
  font-weight: 500; }

.counter-box h6 {
  width: 100%;
  height: 26px;
  line-height: 26px;
  display: block;
  font-size: 22px;
  margin-bottom: 0;
  margin-top: 20px;
  color: #25aae2;
  position: relative; }

.counter-box h6:after {
  content: "";
  width: 100px;
  height: 4px;
  background: #25aae2;
  position: absolute;
  left: calc(50% - 50px);
  bottom: -40px; }

/* SERVICE BOX */
.service-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  position: relative;
  margin: 15px 0;
  background: #0d0d0d; }

.service-box:before {
  content: "";
  width: 100%;
  height: 100%;
  background: #25aae2;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 0;
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out; }

.service-box:hover img {
  opacity: 0;
  transform: scale(1.1); }

.service-box:hover figcaption p {
  margin-bottom: 30px;
  margin-top: 10px;
  opacity: 0.7; }

.service-box:hover figcaption a {
  margin-bottom: 0;
  opacity: 1; }

.service-box:hover:before {
  transform: scale(1.1); }

.service-box * {
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out; }

.service-box img {
  width: 100%;
  position: relative; }

.service-box figcaption {
  width: 100%;
  position: absolute;
  left: 0;
  top: 50%;
  z-index: 1;
  transform: translateY(-50%);
  color: #fff;
  text-align: center; }

.service-box figcaption h6 {
  width: 100%;
  display: block;
  font-size: 36px;
  font-weight: 700;
  margin: 0;
  line-height: 1; }

.service-box figcaption p {
  width: 100%;
  display: block;
  padding: 0 10%;
  margin-bottom: -100px;
  opacity: 0; }

.service-box figcaption a {
  height: 70px;
  line-height: 68px;
  display: inline-block;
  border: 2px solid #fff;
  color: #fff;
  margin-bottom: -100px;
  opacity: 0;
  padding: 0 50px; }

.service-box figcaption a:hover {
  text-decoration: none;
  background: #fff;
  color: #25aae2; }

/* IMAGE OVERLAP BOX */
.image-overlap-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  overflow: hidden; }

.image-overlap-box * {
  transition-duration: 1s;
  -webkit-transition-duration: 1s;
  transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1); }

.image-overlap-box:hover figure img {
  opacity: 0.3;
  transform: scale(1.05); }

.image-overlap-box:hover .content img {
  margin-top: 0;
  margin-bottom: -100px;
  opacity: 0.2;
  transform: scale(1.4); }

.image-overlap-box:hover .content p {
  margin-bottom: 40px;
  opacity: 1; }

.image-overlap-box:hover .content a {
  margin-bottom: 0;
  opacity: 1; }

.image-overlap-box figure {
  width: 100%;
  display: block;
  margin: 0;
  background: #25aae2; }

.image-overlap-box figure img {
  width: 100%; }

.image-overlap-box .content {
  width: 100%;
  position: absolute;
  left: 0;
  top: 50%;
  text-align: center;
  transform: translateY(-50%); }

.image-overlap-box .content img {
  height: 80px;
  display: inline-block;
  margin-bottom: 20px;
  margin-top: 100px; }

.image-overlap-box .content h6 {
  width: 100%;
  display: block;
  font-size: 30px;
  font-weight: 500;
  color: #fff; }

.image-overlap-box .content p {
  width: 100%;
  display: block;
  padding: 0 10%;
  color: #fff;
  margin-bottom: 0;
  opacity: 0; }

.image-overlap-box .content a {
  height: 70px;
  line-height: 68px;
  display: inline-block;
  border: 2px solid #fff;
  color: #fff;
  margin-bottom: -100px;
  opacity: 0;
  padding: 0 50px; }

.image-overlap-box .content a:hover {
  text-decoration: none;
  background: #fff;
  color: #25aae2; }

/* CUSTOM LIST */
.custom-list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin-bottom: 20px; }

.custom-list li {
  width: 100%;
  display: block;
  margin-bottom: 15px;
  padding: 0;
  list-style: none;
  font-family: 'Barlow', sans-serif;
  font-size: 20px; }

.custom-list li:last-child {
  margin-bottom: 0; }

.custom-list li:before {
  content: "\ea54";
  font-family: "LineIcons";
  color: #25aae2;
  font-size: 16px;
  display: inline-block;
  margin-right: 12px; }

/* VIDEO */
.video {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  overflow: hidden;
  background: #0d0d0d; }

.video img {
  width: 100%;
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out; }

.video:hover img {
  opacity: 0.8; }

.video a {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 50%;
  position: absolute;
  left: calc(50% - 60px);
  top: calc(50% - 60px);
  color: #25aae2;
  font-size: 30px; }

.video a:hover {
  text-decoration: none;
  transform: scale(1.1); }

/* IMAGE */
.image {
  width: 100%;
  display: block;
  margin-bottom: 30px; }

.image.spacing {
  margin: 40px 0; }

.image img {
  width: 100%; }

/* TEXT BOX */
.text-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap; }

.text-box h3 {
  width: 100%;
  display: block;
  font-size: 36px;
  font-weight: 700; }

.text-box h5 {
  width: 100%;
  display: block;
  color: #25aae2;
  font-weight: 500;
  margin-bottom: 0;
  font-size: 22px; }

.text-box p {
  width: 100%;
  display: block;
  margin-bottom: 20px; }

/* PASS BOX */
.col-lg-6:nth-child(1) .pass-box {
  border-right: 1px solid rgba(255, 255, 255, 0.1); }

.pass-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  text-align: center;
  color: #fff; }

.pass-box figure {
  width: 100%;
  display: block;
  margin-bottom: 30px; }

.pass-box figure img {
  height: 80px; }

.pass-box h6 {
  width: 100%;
  display: block;
  font-size: 50px;
  font-weight: 800; }

.pass-box p {
  width: 100%;
  display: block;
  padding: 0 20%;
  margin-bottom: 0; }

/* RECENT NEWS */
.recent-news {
  width: 100%;
  display: flex;
  flex-wrap: wrap; }

.recent-news:hover figure img {
  opacity: 0.3;
  transform: scale(1.05); }

.recent-news figure {
  width: 100%;
  display: block;
  margin: 0;
  background: #25aae2;
  position: relative;
  overflow: hidden; }

.recent-news figure img {
  width: 100%;
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out; }

.recent-news .content {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 40px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: none; }

.recent-news .content h3 {
  width: 100%;
  display: block;
  margin-bottom: 15px;
  font-weight: 500;
  font-size: 34px; }

.recent-news .content h3 a {
  display: inline-block;
  color: #0d0d0d; }

.recent-news .content h3 a:hover {
  color: #25aae2;
  text-decoration: none; }

.recent-news .content p {
  width: 100%;
  display: block;
  margin-bottom: 25px;
  opacity: 0.7; }

.recent-news .content small {
  width: 100%;
  display: block;
  font-size: 16px; }

.recent-news .content small span {
  width: 5px;
  height: 5px;
  display: inline-block;
  border-radius: 50%;
  background: #0d0d0d;
  margin: 0 15px;
  transform: translateY(-3px); }

/* BLOG BOX */
.blog-box {
  width: 100%;
  display: block;
  position: relative;
  margin-bottom: 100px; }

.blog-box:last-child {
  margin-bottom: 0; }

.blog-box:hover .content h3 a {
  background-size: 100% 100%; }

.blog-box figure {
  width: 100%;
  display: block;
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;
  background: #25aae2; }

.blog-box figure img {
  width: 100%;
  max-width: inherit; }

.blog-box .content {
  width: 100%;
  height: 100%;
  display: block;
  background: #fff; }

.blog-box .content small {
  display: block;
  font-size: 15px;
  opacity: 0.6;
  margin-bottom: 10px;
  text-transform: uppercase; }

.blog-box .content h3 {
  width: 100%;
  display: block;
  margin-bottom: 20px;
  font-size: 56px;
  line-height: 1.1;
  font-weight: 800; }

.blog-box .content h3 a {
  display: block;
  color: #0d0d0d;
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out; }

.blog-box .content h3 a:hover {
  color: #25aae2;
  text-decoration: none; }

.blog-box .content .author {
  width: 100%;
  display: block;
  margin-bottom: 0;
  font-weight: 500;
  font-size: 20px; }

.blog-box .content .author img {
  height: 70px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 15px; }

.blog-box .content .author b {
  font-weight: 500;
  opacity: 0.6; }

.blog-box .content h6 {
  font-size: 24px;
  line-height: 1.7;
  margin: 30px 0; }

.blog-box .content strong {
  font-weight: 600; }

.blog-box .content figure {
  margin: 30px 0; }

.blog-box .content blockquote {
  width: 100%;
  display: block;
  color: #25aae2;
  font-size: 26px;
  font-family: 'Barlow', sans-serif;
  margin-bottom: 30px; }

.blog-box .content blockquote:before {
  content: "“";
  font-size: 100px;
  height: 50px;
  line-height: 0.9;
  display: block; }

.blog-box .content ul {
  padding-left: 20px; }

.blog-box .content ul li {
  margin: 4px 0; }

.blog-box .content .half-image {
  width: 50%;
  float: right;
  margin-left: 20px; }

.blog-box .content .full-width {
  width: calc(100% + 100px);
  float: left;
  margin-left: -50px;
  margin-right: -50px; }

/* SIDEBAR */
.sidebar {
  width: 100%;
  display: block;
  padding-left: 30px; }

.sidebar .widget {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  border: 1px solid #eee;
  padding: 40px;
  margin-bottom: 40px;
  position: relative; }

.sidebar .widget * {
  position: relative; }

.sidebar .widget .widget-title {
  width: 100%;
  display: block;
  position: relative;
  z-index: 1;
  font-weight: 800;
  letter-spacing: 1px;
  font-size: 22px;
  color: #0d0d0d;
  margin-bottom: 20px;
  padding-bottom: 20px; }

.sidebar .widget .widget-title:before {
  content: "";
  width: 100%;
  height: 2px;
  background: #25aae2;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -1; }

.sidebar .widget form {
  width: 100%;
  display: block;
  margin-top: 10px; }

.sidebar .widget form input[type="submit"] {
  margin-top: 10px;
  background: #0d0d0d;
  color: #fff; }

.sidebar .widget .categories {
  width: 100%;
  display: block;
  margin: 0;
  padding: 0; }

.sidebar .widget .categories li {
  width: 100%;
  display: block;
  margin: 4px 0;
  padding: 0;
  list-style: none; }

.sidebar .widget .categories li a {
  color: #0d0d0d;
  font-size: 19px; }

.sidebar .widget .side-gallery {
  width: calc(100% + 4px);
  float: left;
  margin: 0 -2px;
  padding: 0; }

.sidebar .widget .side-gallery li {
  width: 50%;
  float: left;
  margin: 0;
  padding: 2px;
  list-style: none; }

/* BRANCH BOX */
.branch-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap; }

.branch-box h6 {
  width: 100%;
  display: block;
  font-weight: 800;
  font-size: 24px;
  color: #25aae2; }

.branch-box address {
  width: 100%;
  display: block;
  margin-bottom: 10px; }

.branch-box address b {
  width: 100%;
  display: block;
  margin-top: 5px;
  font-weight: 500; }

.branch-box a {
  display: inline-block;
  text-decoration: underline; }

.branch-box a:hover {
  text-decoration: none;
  color: #25aae2; }

/* MEMBER BOX */
.member-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  position: relative;
  overflow: hidden;
  margin: 0; }

.member-box:hover figcaption {
  bottom: 0;
  transform: translateY(0); }

.member-box img {
  width: 100%;
  display: block; }

.member-box figcaption {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  position: absolute;
  left: 0;
  bottom: 120px;
  color: #fff;
  transform: translateY(100%);
  background: #25aae2;
  text-align: center;
  padding: 30px;
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out; }

.member-box figcaption h6 {
  width: 100%;
  display: block;
  font-size: 34px;
  font-weight: 700; }

.member-box figcaption small {
  width: 100%;
  display: block;
  margin-bottom: 15px; }

.member-box figcaption p {
  width: 100%;
  display: block;
  padding: 0 10%;
  opacity: 0.7; }

.member-box figcaption ul {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 0;
  padding: 0; }

.member-box figcaption ul li {
  display: inline-block;
  margin: 0 7px;
  padding: 0;
  list-style: none; }

.member-box figcaption ul li a {
  color: #fff;
  float: left;
  font-size: 13px; }

/* CTA BOX */
.cta-box {
  width: 100%;
  max-width: 600px;
  display: inline-block;
  margin: 0 auto;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 50px;
  text-align: center; }

.cta-box h2 {
  width: 100%;
  display: block;
  font-size: 50px;
  font-weight: 500; }

.cta-box p {
  width: 100%; }

.cta-box .custom-button {
  margin-top: 10px !important; }

/* TESTIMONIALS */
.testimonial {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  text-align: center; }

.testimonial blockquote {
  width: 100%;
  display: block;
  font-size: 32px;
  font-family: 'Barlow', sans-serif; }

.testimonial p {
  width: 100%;
  display: block; }

.testimonial i {
  display: inline-block;
  margin: 0 3px;
  color: #25aae2; }

.testimonial h6 {
  width: 100%;
  display: block;
  margin: 0; }

.testimonial figure {
  width: 100%;
  display: block;
  margin: 0; }

.testimonial figure img {
  height: 440px; }

/* CONTACT BOX */
.contact-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  font-family: 'Barlow', sans-serif; }

.contact-box li {
  width: 100%;
  display: flex;
  margin-bottom: 15px;
  padding: 0;
  list-style: none;
  line-height: 1.2; }

.contact-box li:last-child {
  margin-bottom: 0; }

.contact-box li h6 {
  width: 100px;
  display: inline-block;
  color: #25aae2;
  margin: 0; }

.contact-box li span {
  display: inline-block; }

.contact-box li a {
  display: inline-block;
  text-decoration: underline; }

/* CONTACT FORM */
.contact-form {
  width: 100%;
  display: block;
  margin: 0;
  padding: 0; }

.contact-form .form-group {
  width: 100%;
  display: block; }

.contact-form .form-group:last-child {
  margin-bottom: 0; }

/* GOOGLE MAPS */
.google-maps {
  width: 100%;
  display: block;
  position: relative; }

.google-maps iframe {
  width: 100%;
  height: 500px;
  display: block;
  border: none;
  filter: grayscale(1);
  position: relative;
  z-index: 0; }

.google-maps .timetable {
  width: 340px;
  display: flex;
  flex-wrap: wrap;
  position: absolute;
  left: 100px;
  top: 50%;
  transform: translateY(-50%);
  background: #0d0d0d;
  padding: 40px;
  margin: 0;
  z-index: 1; }

.google-maps .timetable li {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin: 10px 0;
  padding: 0;
  list-style: none; }

.google-maps .timetable li span {
  color: #fff; }

.google-maps .timetable li b {
  font-weight: 400;
  margin-left: auto;
  color: #25aae2; }

@media only screen and (min-width: 1300px) and (max-width: 2000px) {
  .navbar .hamburger-menu {
    display: none; } }
/* clearfix */
.owl-carousel .owl-wrapper:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: ".";
  line-height: 0; }

/* display none until init */
.owl-carousel {
  position: relative;
  display: none;
  width: 100%;
  -ms-touch-action: pan-y; }

.owl-carousel .owl-wrapper {
  position: relative;
  display: none;
  -webkit-transform: translate3d(0px, 0px, 0px); }

.owl-carousel .owl-wrapper-outer {
  position: relative;
  overflow: hidden;
  width: 100%; }

.owl-carousel .owl-wrapper-outer.autoHeight {
  -webkit-transition: height 500ms ease-in-out;
  -moz-transition: height 500ms ease-in-out;
  -ms-transition: height 500ms ease-in-out;
  -o-transition: height 500ms ease-in-out;
  transition: height 500ms ease-in-out; }

.owl-carousel2 .owl-wrapper-outer {
  position: relative;
  overflow: hidden;
  width: 100%;
  display: inline-flex; }

.owl-carousel2 .owl-wrapper-outer.autoHeight {
  -webkit-transition: height 500ms ease-in-out;
  -moz-transition: height 500ms ease-in-out;
  -ms-transition: height 500ms ease-in-out;
  -o-transition: height 500ms ease-in-out;
  transition: height 500ms ease-in-out; }

.owl-carousel .owl-item {
  float: left; }

.owl-controls .owl-page,
.owl-controls .owl-buttons div {
  cursor: pointer; }

.owl-controls {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }

/* mouse grab icon */
.grabbing {
  cursor: url(../images/grabbing.png) 8 8, move; }

/* fix */
.owl-carousel .owl-wrapper,
.owl-carousel .owl-item {
  -webkit-animation: goDown .6s ease both;
  -moz-animation: goDown .6s ease both;
  animation: goDown .6s ease both;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  display: inline-table; }

.owl-theme .owl-controls {
  margin-top: 10px;
  text-align: center; }

/* 
 *  Owl Carousel CSS3 Transitions 
 *  v1.3.2
 */
.owl-origin {
  -webkit-perspective: 1200px;
  -webkit-perspective-origin-x: 50%;
  -webkit-perspective-origin-y: 50%;
  -moz-perspective: 1200px;
  -moz-perspective-origin-x: 50%;
  -moz-perspective-origin-y: 50%;
  perspective: 1200px; }

/* fade */
.owl-fade-out {
  z-index: 10;
  -webkit-animation: fadeOut .7s both ease;
  -moz-animation: fadeOut .7s both ease;
  animation: fadeOut .7s both ease; }

.owl-fade-in {
  -webkit-animation: fadeIn .7s both ease;
  -moz-animation: fadeIn .7s both ease;
  animation: fadeIn .7s both ease; }

/* backSlide */
.owl-backSlide-out {
  -webkit-animation: backSlideOut 1s both ease;
  -moz-animation: backSlideOut 1s both ease;
  animation: backSlideOut 1s both ease; }

.owl-backSlide-in {
  -webkit-animation: backSlideIn 1s both ease;
  -moz-animation: backSlideIn 1s both ease;
  animation: backSlideIn 1s both ease; }

/* goDown */
.owl-goDown-out {
  -webkit-animation: scaleToFade .7s ease both;
  -moz-animation: scaleToFade .7s ease both;
  animation: scaleToFade .7s ease both; }

.owl-goDown-in {
  -webkit-animation: goDown .6s ease both;
  -moz-animation: goDown .6s ease both;
  animation: goDown .6s ease both; }

/* scaleUp */
.owl-fadeUp-in {
  -webkit-animation: scaleUpFrom .5s ease both;
  -moz-animation: scaleUpFrom .5s ease both;
  animation: scaleUpFrom .5s ease both; }

.owl-fadeUp-out {
  -webkit-animation: scaleUpTo .5s ease both;
  -moz-animation: scaleUpTo .5s ease both;
  animation: scaleUpTo .5s ease both; }

/* Keyframes */
/*empty*/
@-webkit-keyframes empty {
  0% {
    opacity: 1; } }
@-moz-keyframes empty {
  0% {
    opacity: 1; } }
@keyframes empty {
  0% {
    opacity: 1; } }
@-webkit-keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
@-moz-keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
@-webkit-keyframes fadeOut {
  0% {
    opacity: 1; }
  100% {
    opacity: 0; } }
@-moz-keyframes fadeOut {
  0% {
    opacity: 1; }
  100% {
    opacity: 0; } }
@keyframes fadeOut {
  0% {
    opacity: 1; }
  100% {
    opacity: 0; } }
@-webkit-keyframes backSlideOut {
  25% {
    opacity: .5;
    -webkit-transform: translateZ(-500px); }
  75% {
    opacity: .5;
    -webkit-transform: translateZ(-500px) translateX(-200%); }
  100% {
    opacity: .5;
    -webkit-transform: translateZ(-500px) translateX(-200%); } }
@-moz-keyframes backSlideOut {
  25% {
    opacity: .5;
    -moz-transform: translateZ(-500px); }
  75% {
    opacity: .5;
    -moz-transform: translateZ(-500px) translateX(-200%); }
  100% {
    opacity: .5;
    -moz-transform: translateZ(-500px) translateX(-200%); } }
@keyframes backSlideOut {
  25% {
    opacity: .5;
    transform: translateZ(-500px); }
  75% {
    opacity: .5;
    transform: translateZ(-500px) translateX(-200%); }
  100% {
    opacity: .5;
    transform: translateZ(-500px) translateX(-200%); } }
@-webkit-keyframes backSlideIn {
  0%,
  25% {
    opacity: .5;
    -webkit-transform: translateZ(-500px) translateX(200%); }
  75% {
    opacity: .5;
    -webkit-transform: translateZ(-500px); }
  100% {
    opacity: 1;
    -webkit-transform: translateZ(0) translateX(0); } }
@-moz-keyframes backSlideIn {
  0%,
  25% {
    opacity: .5;
    -moz-transform: translateZ(-500px) translateX(200%); }
  75% {
    opacity: .5;
    -moz-transform: translateZ(-500px); }
  100% {
    opacity: 1;
    -moz-transform: translateZ(0) translateX(0); } }
@keyframes backSlideIn {
  0%,
  25% {
    opacity: .5;
    transform: translateZ(-500px) translateX(200%); }
  75% {
    opacity: .5;
    transform: translateZ(-500px); }
  100% {
    opacity: 1;
    transform: translateZ(0) translateX(0); } }
@-webkit-keyframes scaleToFade {
  to {
    opacity: 0;
    -webkit-transform: scale(0.8); } }
@-moz-keyframes scaleToFade {
  to {
    opacity: 0;
    -moz-transform: scale(0.8); } }
@keyframes scaleToFade {
  to {
    opacity: 0;
    transform: scale(0.8); } }
@-webkit-keyframes goDown {
  from {
    -webkit-transform: translateY(-100%); } }
@-moz-keyframes goDown {
  from {
    -moz-transform: translateY(-100%); } }
@keyframes goDown {
  from {
    transform: translateY(-100%); } }
@-webkit-keyframes scaleUpFrom {
  from {
    opacity: 0;
    -webkit-transform: scale(1.5); } }
@-moz-keyframes scaleUpFrom {
  from {
    opacity: 0;
    -moz-transform: scale(1.5); } }
@keyframes scaleUpFrom {
  from {
    opacity: 0;
    transform: scale(1.5); } }
@-webkit-keyframes scaleUpTo {
  to {
    opacity: 0;
    -webkit-transform: scale(1.5); } }
@-moz-keyframes scaleUpTo {
  to {
    opacity: 0;
    -moz-transform: scale(1.5); } }
@keyframes scaleUpTo {
  to {
    opacity: 0;
    transform: scale(1.5); } }
/* Styling Next and Prev buttons */
.owl-theme .owl-controls .owl-buttons div {
  display: inline-block;
  margin: 5px;
  padding: 3px 10px;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  background: #869791;
  color: #FFF;
  font-size: 12px;
  opacity: 0.5;
  filter: Alpha(Opacity=50);
  /*IE7 fix*/
  zoom: 1;
  display: inline;
  /*IE7 life-saver */ }

/* Clickable class fix problem with hover on touch devices */
/* Use it for non-touch hover action */
.owl-theme .owl-controls.clickable .owl-buttons div:hover {
  text-decoration: none;
  opacity: 1;
  filter: Alpha(Opacity=100);
  /*IE7 fix*/ }

/* Styling Pagination*/
.owl-theme .owl-controls .owl-page {
  display: inline-block;
  zoom: 1;
  /*IE7 life-saver */ }

.owl-theme .owl-controls .owl-page span {
  display: block;
  margin: 5px 7px;
  width: 12px;
  height: 12px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  border-radius: 20px;
  background: #869791;
  opacity: 0.5;
  filter: Alpha(Opacity=50);
  /*IE7 fix*/ }

.owl-theme .owl-controls .owl-page.active span,
.owl-theme .owl-controls.clickable .owl-page:hover span {
  opacity: 1;
  filter: Alpha(Opacity=100);
  /*IE7 fix*/ }

/* If PaginationNumbers is true */
.owl-theme .owl-controls .owl-page span.owl-numbers {
  padding: 2px 10px;
  width: auto;
  height: auto;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  color: #FFF;
  font-size: 12px; }

/* preloading images */
.owl-item.loading {
  min-height: 150px;
  background: url(../images/AjaxLoader.gif) no-repeat center center; }

/* CUSTOM USER OPTIONS */
.carousel-container {
  width: 100%;
  margin-top: 50px; }

.item {
  margin: 0;
  background: #fff;
  color: #f0f0f0;
  border-radius: 10px;
  text-align: center;
  font-family: "Poppins", sans-serif; }
  @media only screen and (max-width: 56.25em) {
    .item {
      font-size: 4.5rem; } }

.item img {
  width: 100%; }

.item h4 {
  margin-top: 27px;
  color: #0c52aa;
  font-size: 2rem;
  font-family: "poppins"; }
  @media only screen and (max-width: 56.25em) {
    .item h4 {
      font-size: 6.7rem; } }

.item h6 {
  padding-bottom: 30px;
  color: #575a5a;
  font-size: 1.5rem;
  font-family: "poppins"; }
  @media only screen and (max-width: 56.25em) {
    .item h6 {
      font-size: 3.8rem; } }

.customNavigation {
  display: none;
  margin-top: -25px;
  text-align: center; }

.customNavigation .btn {
  margin: 0;
  position: relative;
  display: inline-block;
  overflow: visible;
  padding: 0.8em 1.1em;
  width: 50%;
  float: left;
  border: 0;
  box-shadow: none;
  color: #fff;
  text-transform: uppercase;
  text-shadow: none;
  font-size: 20px;
  cursor: crosshair;
  -webkit-transition: all .1s ease-in 0s;
  -moz-transition: all .1s ease-in 0s;
  -o-transition: all .1s ease-in 0s;
  -webkit-font-smoothing: antialiased;
  background: #333; }

.customNavigation .btn:hover {
  background: #222; }

.hoverfx {
  display: block;
  overflow: hidden;
  text-align: center; }

.hoverfx img {
  position: absolute;
  left: 0;
  width: 100%;
  border-radius: 1px;
  margin-left: 1px;
  filter: alpha(opacity=40);
  opacity: 0.6;
  transition: all .5s ease-out;
  border-color: #cecece;
  border-style: solid;
  border-width: 1.5rem; }
  .hoverfx img:hover {
    opacity: 1;
    transform: translateY(5px); }

.hoverfx:before {
  display: inline-block;
  padding-top: 80%;
  content: '';
  vertical-align: middle; }

.hoverfx .figure {
  position: relative;
  z-index: 2;
  display: inline-block;
  padding: 12px;
  max-width: 60%;
  border: 3px solid #e9e9e9;
  color: #ecf0f1;
  vertical-align: middle;
  text-transform: uppercase;
  font-size: 1rem;
  opacity: 0;
  -webkit-transition: all .3s ease;
  -moz-transition: all .3s ease;
  -o-transition: all .3s ease; }

.hoverfx .overlay {
  position: absolute;
  top: 0;
  z-index: 1;
  padding: 50%;
  margin-left: 5px;
  border-radius: 10px;
  background: #fff;
  opacity: 0;
  -webkit-transition: all .3s ease;
  -moz-transition: all .3s ease;
  -o-transition: all .3s ease;
  display: none; }

.item:hover .figure,
.item:hover .overlay {
  opacity: 1; }

.owl-wrapper {
  width: 100%;
  left: 0px;
  display: block; }

.containerTitles {
  width: 100%;
  text-align: center;
  margin: 20px 0; }

.containerTitles h1 {
  font-size: 3rem;
  letter-spacing: 1px;
  text-transform: uppercase; }

.containerTitles h2 {
  font-weight: normal; }

.component-systemTabs {
  width: 100%;
  display: flex; }

.component-systemTabs .tabs-container {
  margin: 0 auto;
  width: 100%; }

.component-systemTabs .tabs-container ul.tabs {
  margin: 20px 0;
  padding: 0px;
  list-style: none;
  text-align: center; }

.component-systemTabs .tabs-container ul.tabs li {
  background: none;
  color: #5a5a5a;
  display: inline-block;
  padding: 10px 25px;
  cursor: pointer;
  font-weight: 600;
  border-bottom: 4px solid #d7d7d7;
  font-size: 1.6rem;
  transition: all 0.5s ease; }
  @media only screen and (max-width: 56.25em) {
    .component-systemTabs .tabs-container ul.tabs li {
      font-size: 4.1rem; } }

.component-systemTabs .tabs-container ul.tabs li:hover {
  color: #2e276a; }

.component-systemTabs .tabs-container ul.tabs li.current {
  color: #2e276a;
  border-bottom: 4px solid #2e276a;
  transition: all 0.5s ease; }

.component-systemTabs .tab-content {
  display: none;
  padding: 15px;
  width: 100%; }

.component-systemTabs .tab-content.current {
  display: block; }

.component-systemTabs .cards {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  width: 100%; }

.component-systemTabs .cards .card {
  width: 454px;
  height: 507px;
  margin: 5px;
  position: relative; }

.component-systemTabs .cards .card img {
  height: 100%;
  width: 100%;
  max-width: 454px;
  max-height: 507px; }

.component-systemTabs .cards .card .cardContent {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  position: absolute;
  background-color: rgba(29, 29, 30, 0.555);
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  padding: 20px;
  border: .7rem solid transparent;
  border-image: linear-gradient(to right, #000000, #202020);
  border-image-slice: 1;
  cursor: pointer;
  transition: all 0.3s ease; }

.component-systemTabs .cards .card .cardContent h2 {
  color: white;
  font-size: 2rem; }

.component-systemTabs .cards .card .cardContent button {
  border: 0;
  background-color: #000000;
  padding: 7px 10px;
  width: 100px;
  color: white;
  font-size: 1.2rem;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  font-weight: 900;
  cursor: pointer;
  outline: none;
  transition: all 0.3s ease;
  opacity: 0;
  transition: 0.3s ease; }

.component-systemTabs .cards .card .cardContent button:hover {
  -webkit-box-shadow: 0px 0px 9px 0px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 0px 0px 9px 0px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 0px 9px 0px rgba(0, 0, 0, 0.3);
  font-size: 1.25rem; }

.component-systemTabs .cards .card .cardContent:hover {
  background-color: rgba(0, 0, 0, 0.2); }

.component-systemTabs .cards .card .cardContent:hover button {
  opacity: 1; }

/* Count */
.counter {
  text-align: center; }

.counter-count {
  font-size: 50px;
  font-weight: bold;
  position: relative;
  color: #ffffff;
  text-align: center;
  display: inline-block; }

h3.counter {
  text-align: center;
  color: #ffffff; }

span.plus {
  color: #ffffff;
  font-size: 2.5em; }

.form__grup {
  margin-bottom: .2rem; }
  .form__grup__grup:not(:last-child) {
    margin-bottom: .5rem; }
.form__input {
  font-size: 1.5rem;
  font-family: inherit;
  padding: 1.3rem 2rem;
  border-radius: .2rem;
  color: inherit;
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  border-bottom: .3rem solid transparent;
  width: 90%;
  display: block;
  transition: all .3s;
  box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1);
  border-bottom: 0.3rem solid #373075; }
  @media only screen and (max-width: 56.25em) {
    .form__input {
      font-size: 3.5rem;
      padding: 2.3rem 2rem; } }
  @media only screen and (max-width: 40em) {
    .form__input {
      font-size: 3.5rem;
      padding: 2.3rem 2rem; } }
  .form__input:focus {
    outline: none;
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1);
    border-bottom: 0.3rem solid #373075; }
  .form__input:focus:invalid {
    border-bottom: 0.3rem solid #2e276a; }
  .form__input::-webkit-input-placeholder {
    color: #999; }
.form__label {
  font-size: 1.2rem;
  font-weight: 700;
  margin-left: 2rem;
  margin-top: .7rem;
  display: block;
  transition: all .3s; }
.form__input:placeholder-shown + .form__label {
  opacity: 0;
  visibility: hidden;
  transform: translateY(-4rem); }
.form__radio-grup {
  width: 49%;
  display: inline-block;
  float: left;
  margin-bottom: 2rem; }
.form__radio-input {
  display: none; }
.form__radio-label {
  font-size: 1.6rem;
  cursor: pointer;
  position: relative;
  padding-left: 4.5rem; }
.form__radio-buton {
  height: 3rem;
  width: 3rem;
  border: 0.5rem solid #373075;
  border-radius: 50%;
  display: inline-block;
  position: absolute;
  left: 0;
  top: -.4rem; }
  .form__radio-buton::after {
    content: "";
    display: block;
    height: 1.3rem;
    width: 1.3rem;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #373075;
    opacity: 0;
    transition: opacity .2s; }
.form__radio-input:checked + .form__radio-label .form__radio-buton::after {
  opacity: 1; }

.form-popup {
  padding: 0rem 5rem 0rem 5rem;
  text-align: center; }
  .form-popup__grup {
    margin-bottom: .2rem; }
    .form-popup__grup__grup:not(:last-child) {
      margin-bottom: .5rem; }
  .form-popup__input {
    font-size: 1.5rem;
    font-family: inherit;
    padding: 1.3rem 2rem;
    border-radius: .2rem;
    color: #1a1a1a;
    background-color: rgba(255, 255, 255, 0.5);
    border: none;
    border-bottom: .3rem solid transparent;
    width: 100%;
    display: block;
    transition: all .3s;
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1);
    border-bottom: 0.3rem solid #3b3664; }
    @media only screen and (max-width: 56.25em) {
      .form-popup__input {
        font-size: 3.5rem;
        padding: 2.3rem 2rem; } }
    @media only screen and (max-width: 40em) {
      .form-popup__input {
        font-size: 3.5rem;
        padding: 2.3rem 2rem; } }
    .form-popup__input:focus {
      outline: none;
      box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1);
      border-bottom: 0.3rem solid #373075; }
    .form-popup__input:focus:invalid {
      border-bottom: 0.3rem solid #2e276a; }
    .form-popup__input::-webkit-input-placeholder {
      color: #999; }
  .form-popup__label {
    font-size: 1.2rem;
    font-weight: 700;
    margin-left: 2rem;
    margin-top: .7rem;
    display: block;
    transition: all .3s; }
  .form-popup__input:placeholder-shown + .form-popup__label {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-4rem); }
  .form-popup__radio-grup {
    width: 49%;
    display: inline-block;
    float: left;
    margin-bottom: 2rem; }
  .form-popup__radio-input {
    display: none; }
  .form-popup__radio-label {
    font-size: 1.6rem;
    cursor: pointer;
    position: relative;
    padding-left: 4.5rem; }
  .form-popup__radio-buton {
    height: 3rem;
    width: 3rem;
    border: 0.5rem solid #373075;
    border-radius: 50%;
    display: inline-block;
    position: absolute;
    left: 0;
    top: -.4rem; }
    .form-popup__radio-buton::after {
      content: "";
      display: block;
      height: 1.3rem;
      width: 1.3rem;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: #373075;
      opacity: 0;
      transition: opacity .2s; }
  .form-popup__radio-input:checked + .form-popup__radio-label .form-popup__radio-buton::after {
    opacity: 1; }

.icon-bar {
  position: fixed;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 8; }

.icon-bar a {
  display: block;
  text-align: center;
  padding: 16px;
  transition: all 0.3s ease;
  color: white;
  font-size: 20px; }

.icon-bar a:hover {
  background-color: #000; }

.facebook {
  background: #3B5998;
  color: white; }

.twitter {
  background: #55ACEE;
  color: white; }

.google {
  background: #dd4b39;
  color: white; }

.linkedin {
  background: #007bb5;
  color: white; }

.youtube {
  background: #bb0000;
  color: white; }

.content {
  margin-left: 75px;
  font-size: 30px; }

header .item {
  position: relative;
  margin-top: 87px; }

header .item img {
  width: 100%;
  height: 100%;
  object-fit: cover; }

header .item .cover {
  padding: 75px 0;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0);
  display: flex;
  align-items: center; }

header .item .cover .header-content {
  position: relative;
  padding: 56px;
  overflow: hidden;
  margin-top: 8px; }
  @media only screen and (max-width: 56.25em) {
    header .item .cover .header-content {
      padding: 0px;
      margin-top: -7rem;
      margin-left: 0rem; } }

header .item .cover .header-content .line {
  content: "";
  display: inline-block;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  position: absolute; }

header .item .cover .header-content h2 {
  font-weight: 900;
  font-size: 50px;
  color: #fff;
  text-align: center;
  transform: translateY(-0.3rem);
  animation: butonefekt .2s ease-out .20s;
  animation-fill-mode: backwards;
  transition: all 1s; }
  @media only screen and (max-width: 1024px) {
    header .item .cover .header-content h2 {
      font-size: 46px; } }
  @media only screen and (max-width: 56.25em) {
    header .item .cover .header-content h2 {
      font-size: 30px;
      font-weight: 900;
      width: 100%;
      text-align: center; } }

header .item .cover .header-content h1 {
  font-size: 20px;
  font-weight: 600;
  word-spacing: 3px;
  color: #fff;
  padding: 5px;
  text-align: center;
  margin-bottom: 1rem;
  transform: translateY(-0.3rem);
  animation: butonefekt .3s ease-out .60s;
  animation-fill-mode: backwards;
  transition: all 1s; }
  @media only screen and (max-width: 1024px) {
    header .item .cover .header-content h1 {
      font-size: 25px; } }
  @media only screen and (max-width: 56.25em) {
    header .item .cover .header-content h1 {
      font-size: 17px;
      font-weight: 500;
      width: 100%;
      padding: 1px; } }

header .item .cover .header-content h4 {
  font-size: 25px;
  font-weight: 600;
  line-height: 40px;
  color: #fff; }
  @media only screen and (max-width: 1024px) {
    header .item .cover .header-content h4 {
      font-size: 30px; } }
  @media only screen and (max-width: 56.25em) {
    header .item .cover .header-content h4 {
      font-size: 17px; } }

header .owl-item.active h1 {
  -webkit-animation-duration: 50s;
  animation-duration: 0.8s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  animation-name: fadeInDown;
  animation-delay: 0.4s; }

header .owl-item.active h2 {
  -webkit-animation-duration: 50s;
  animation-duration: 0.8s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  animation-name: fadeInDown;
  animation-delay: 0.4s; }

header .owl-item.active h4 {
  -webkit-animation-duration: 50s;
  animation-duration: 0.8s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  animation-name: fadeInUp;
  animation-delay: 0.4s; }

header .owl-item.active .line {
  -webkit-animation-duration: 5s;
  animation-duration: 0.8s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  animation-name: fadeInLeft;
  animation-delay: 0.4s; }

header .owl-nav .owl-prev {
  position: absolute;
  left: 15px;
  top: 30%;
  opacity: 0;
  -webkit-transition: all 0.4s ease-out;
  transition: all 0.4s ease-out;
  width: 40px;
  cursor: pointer;
  height: 40px;
  position: absolute;
  display: block;
  z-index: 1000;
  border-radius: 0; }

header .owl-nav .owl-prev span {
  font-size: 8.6875rem;
  color: #fff; }

header .owl-nav .owl-prev:focus {
  outline: 0; }

header .owl-nav .owl-next {
  position: absolute;
  right: 15px;
  top: 30%;
  opacity: 0;
  -webkit-transition: all 0.4s ease-out;
  transition: all 0.4s ease-out;
  width: 40px;
  cursor: pointer;
  height: 40px;
  position: absolute;
  display: block;
  z-index: 1000;
  border-radius: 0; }

header .owl-nav .owl-next span {
  font-size: 8.6875rem;
  color: #fff; }

header .owl-nav .owl-next:focus {
  outline: 0; }

header:hover .owl-prev {
  left: 0px;
  opacity: 1; }

header:hover .owl-next {
  right: 0px;
  opacity: 1; }

.slider {
  position: relative;
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: flex-end; }

.slider input[type="radio"] {
  position: relative;
  z-index: 888;
  margin: 5px;
  margin-bottom: 40px;
  outline: none;
  cursor: pointer; }

.yukaricik {
  position: fixed;
  width: 5rem;
  height: 5rem;
  background-image: linear-gradient(to right bottom, #2e276a, #373075);
  bottom: 0rem;
  right: 5rem;
  text-decoration: none;
  text-align: center;
  line-height: 5rem;
  display: inline-block;
  z-index: 0; }
  @media only screen and (max-width: 40em) {
    .yukaricik {
      display: none; } }
  @media only screen and (max-width: 75em) {
    .yukaricik {
      display: none; } }

.yukaricik-icon {
  font-size: 2rem;
  margin-bottom: .5rem;
  display: inline-block;
  color: #fff; }
  @media only screen and (max-width: 40em) {
    .yukaricik-icon {
      display: none; } }
  @media only screen and (max-width: 75em) {
    .yukaricik-icon {
      display: none; } }

#top {
  bottom: 5px;
  bottom: -41px;
  display: none;
  height: 100px;
  position: fixed;
  right: 20px;
  z-index: 5; }

* {
  outline: none !important; }

body {
  margin: 0;
  padding: 0;
  font-family: "Mohave";
  font-size: 18px;
  color: #2e276a;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased; }

/* LINKS */
a {
  color: #2e276a;
  -webkit-transition: all .35s ease-in-out;
  -moz-transition: all .35s ease-in-out;
  -ms-transition: all .35s ease-in-out;
  -o-transition: all .35s ease-in-out;
  transition: all .35s ease-in-out; }

a:hover {
  text-decoration: underline;
  color: #2e276a; }

/* HTML TAGS */
img {
  max-width: 100%; }

p {
  font-family: 'Barlow', sans-serif; }

/* FORM ELEMENTS */
input[type="text"] {
  width: 420px;
  max-width: 100%;
  height: 70px;
  padding: 0 30px;
  border: 1px solid #cecece; }

input[type="email"] {
  width: 420px;
  max-width: 100%;
  height: 70px;
  padding: 0 30px;
  border: 1px solid #cecece; }

input[type="search"] {
  width: 420px;
  max-width: 100%;
  height: 70px;
  padding: 0 30px;
  border: 1px solid #cecece; }

input[type="password"] {
  width: 420px;
  max-width: 100%;
  height: 70px;
  padding: 0 30px;
  border: 1px solid #cecece; }

input[type="radio"] {
  width: 18px;
  height: 18px;
  display: inline-block;
  margin-right: 4px;
  transform: translateY(3px);
  appearance: none;
  background: #ededed;
  border-radius: 50%; }

input[type="radio"]:checked {
  border: 6px solid #2e276a; }

input[type="checkbox"] {
  width: 18px;
  height: 18px;
  display: inline-block;
  margin-right: 4px;
  transform: translateY(3px);
  appearance: none;
  background: #ededed; }

input[type="checkbox"]:checked {
  border: 6px solid #2e276a; }

textarea {
  width: 520px;
  max-width: 100%;
  height: 140px;
  padding: 30px;
  border: 1px solid #cecece; }

select {
  width: 420px;
  max-width: 100%;
  height: 70px;
  padding: 0 30px;
  border: 1px solid #cecece; }

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: linear-gradient(45deg, transparent 50%, gray 50%), linear-gradient(135deg, gray 50%, transparent 50%), linear-gradient(to right, #ccc, #ccc);
  background-position: calc(100% - 30px) 34px, calc(100% - 25px) 34px, calc(100% - 3.5em) 20px;
  background-size: 5px 5px, 5px 5px, 1px 40px;
  background-repeat: no-repeat; }

select:focus {
  background-image: linear-gradient(45deg, gray 50%, transparent 50%), linear-gradient(135deg, transparent 50%, gray 50%), linear-gradient(to right, #ccc, #ccc);
  background-position: calc(100% - 25px) 34px, calc(100% - 30px) 34px, calc(100% - 3.5em) 20px;
  background-size: 5px 5px, 5px 5px, 1px 40px;
  background-repeat: no-repeat;
  border-color: gray;
  outline: 0; }

select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #000; }

input[type="submit"] {
  height: 70px;
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  background: #2e276a;
  border: none;
  padding: 0 50px; }

button[type="submit"] {
  height: 70px;
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  background: #2e276a;
  border: none;
  padding: 0 50px; }
  button[type="submit"] i {
    display: inline-block;
    margin-right: 8px;
    font-size: 18px;
    transform: translateY(2px); }

/* CUSTOM CONTAINER */
@media (min-width: 1170px) {
  .container {
    max-width: 1100px; } }
@media (min-width: 1280px) {
  .container {
    max-width: 1260px; } }
/* CUSTOM CLASSES */
.overflow {
  overflow: hidden; }

.no-gutters {
  padding: 0;
  margin: 0; }

/* SPACING */
.no-spacing {
  margin: 0 !important;
  padding: 0 !important; }

.no-top-spacing {
  margin-top: 0 !important;
  padding-top: 0 !important; }

.no-bottom-spacing {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important; }

/* HAMBURGER MENU */
.hamburger-menu {
  width: 30px;
  height: 20px;
  position: relative;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  transform: rotate(0deg);
  transition-duration: 500ms;
  -webkit-transition-duration: 500ms;
  transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
  cursor: pointer; }

.hamburger-menu span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: #fff;
  opacity: 1;
  left: 0;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  transform: rotate(0deg);
  -webkit-transition: .25s ease-in-out;
  -moz-transition: .25s ease-in-out;
  -o-transition: .25s ease-in-out;
  transition: .25s ease-in-out; }

.hamburger-menu span:nth-child(1) {
  top: 0px;
  width: 100%; }

.hamburger-menu span:nth-child(2) {
  top: 9px;
  width: 22px; }

.hamburger-menu span:nth-child(3) {
  top: 18px;
  width: 100%; }

.hamburger-menu:hover span {
  width: 100% !important; }

.hamburger-menu.open span {
  width: 20px !important; }

.hamburger-menu.open span:nth-child(1) {
  top: 9px;
  right: 0;
  -webkit-transform: rotate(135deg);
  -moz-transform: rotate(135deg);
  -o-transform: rotate(135deg);
  transform: rotate(135deg);
  width: 28px !important; }

.hamburger-menu.open span:nth-child(2) {
  opacity: 0;
  left: -20px; }

.hamburger-menu.open span:nth-child(3) {
  top: 9px;
  right: 0;
  -webkit-transform: rotate(-135deg);
  -moz-transform: rotate(-135deg);
  -o-transform: rotate(-135deg);
  transform: rotate(-135deg);
  width: 28px !important; }

/* ODOMETER */
.odometer.odometer-auto-theme {
  padding: 0; }

.odometer.odometer-auto-theme .odometer-digit, .odometer.odometer-theme-car .odometer-digit {
  padding: 0; }

.odometer.odometer-auto-theme .odometer-digit .odometer-digit-inner, .odometer.odometer-theme-car .odometer-digit .odometer-digit-inner {
  left: -4px; }

/* SWIPER PAGINATION */
.swiper-pagination {
  width: 100%; }
  .swiper-pagination .swiper-pagination-bullet {
    width: 3vw;
    height: 4px;
    background: #fff;
    -webkit-transition: all .35s ease-in-out;
    -moz-transition: all .35s ease-in-out;
    -ms-transition: all .35s ease-in-out;
    -o-transition: all .35s ease-in-out;
    transition: all .35s ease-in-out;
    opacity: 0.8;
    border-radius: 5px; }
    .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
      width: 6vw;
      background: #c1c1c1;
      opacity: 1; }

/* CUSTOM BUTTON */
.col-12.text-center .custom-button {
  margin-top: 50px; }

.custom-button {
  height: 70px;
  line-height: 70px;
  display: inline-block;
  background: #2e276a;
  color: #fff;
  padding: 0 50px;
  position: relative;
  font-size: 16px;
  border-radius: 55px; }
  .custom-button:before {
    content: "";
    width: 0;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.05;
    transition-duration: 1s;
    -webkit-transition-duration: 1s;
    transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
    -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1); }
  .custom-button:hover {
    text-decoration: none;
    color: #fff; }
  .custom-button:hover:before {
    width: 100%; }

.col-12.text-center .custom-buttonw {
  margin-top: 50px; }

.custom-buttonw {
  height: 70px;
  line-height: 70px;
  display: inline-block;
  background: #fff;
  color: #2e276a;
  padding: 0 50px;
  position: relative;
  font-size: 16px;
  border-radius: 55px; }
  .custom-buttonw:before {
    content: "";
    width: 0;
    height: 100%;
    background: #000;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.05;
    transition-duration: 1s;
    -webkit-transition-duration: 1s;
    transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
    -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1); }
  .custom-buttonw:hover {
    text-decoration: none;
    color: #2e276a; }
  .custom-buttonw:hover:before {
    width: 100%; }

/* REVEAL EFFECT */
.wow.fade {
  opacity: 0;
  transition: opacity 0.5s ease;
  transition-delay: 0.2s; }

.wow.fade.animated {
  opacity: 1; }

.reveal-effect {
  float: left;
  position: relative; }
  .reveal-effect.animated:before {
    content: "";
    width: 100%;
    height: 100%;
    background: #eee;
    position: absolute;
    left: 0;
    top: 0;
    animation: 1s reveal linear forwards;
    -webkit-animation-duration: 1s;
    z-index: 1;
    -moz-animation-duration: 1s;
    -ms-animation-duration: 1s;
    -o-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: forwards;
    -moz-animation-fill-mode: forwards;
    -ms-animation-fill-mode: forwards;
    -o-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
    -moz-animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
    -o-animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
    -ms-animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
    animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86); }

.reveal-effect.animated > * {
  animation: 1s reveal-inner linear forwards; }

@-webkit-keyframes reveal {
  0% {
    left: 0;
    width: 0; }
  50% {
    left: 0;
    width: 100%; }
  51% {
    left: auto;
    right: 0; }
  100% {
    left: auto;
    right: 0;
    width: 0; } }
@-webkit-keyframes reveal-inner {
  0% {
    visibility: hidden;
    opacity: 0; }
  50% {
    visibility: hidden;
    opacity: 0; }
  51% {
    visibility: visible;
    opacity: 1; }
  100% {
    visibility: visible;
    opacity: 1; } }
/* PRELOADER */
.preloader {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  position: fixed;
  z-index: 99;
  right: 0;
  top: 0;
  background: #2e276a;
  transition-duration: 1s;
  -webkit-transition-duration: 1s;
  transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1); }
  .preloader figure {
    width: 140px;
    height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    animation: fadeup 0.30s;
    position: relative; }
    .preloader figure:after {
      content: "";
      width: 100%;
      height: 100%;
      border: 1px solid transparent;
      border-top: 1px solid #fff;
      border-radius: 50%;
      position: absolute;
      left: 0;
      top: 0;
      animation: rotate1 0.60s infinite; }
  .preloader img {
    height: 50px;
    display: inline-block; }

.page-loaded .preloader {
  top: -100%; }

@keyframes fadeup {
  0% {
    transform: translateY(20px);
    opacity: 0; }
  100% {
    transform: translateY(0);
    opacity: 1; } }
@keyframes rotate1 {
  0% {
    transform: rotate(0deg); }
  100% {
    transform: rotate(360deg); } }
/* PAGE TRANSITION */
.page-transition {
  width: 100%;
  height: 0;
  position: fixed;
  z-index: 99;
  left: 0;
  bottom: 0;
  background: #2e276a;
  transition-duration: 1s;
  -webkit-transition-duration: 1s;
  transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1); }
  .page-transition.active {
    height: 100%; }

/* SIDE WIDGET */
.side-widget {
  width: 400px;
  height: 100vh;
  max-width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  position: fixed;
  left: -100%;
  top: 0;
  transition-duration: 1s;
  -webkit-transition-duration: 1s;
  transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
  background: #2e276a;
  z-index: 6;
  box-shadow: 0 0 60px rgba(0, 0, 0, 0.4);
  padding: 20px 30px;
  color: #fff; }
  .side-widget .inner {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    overflow-y: auto;
    height: 100%; }
  .side-widget .logo {
    width: 100%;
    display: block;
    margin-bottom: 40px; }
    .side-widget .logo img {
      height: 49px; }
  .side-widget .show-mobile {
    display: none; }
  .side-widget .hide-mobile {
    width: 100%;
    display: inline-block; }
  .side-widget .gallery {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px; }
    .side-widget .gallery a {
      width: 50%;
      padding-right: 3px; }
      .side-widget .gallery a:last-child {
        padding-left: 3px; }
  .side-widget p {
    width: 100%;
    display: block;
    color: #fff; }
  .side-widget .widget-title {
    width: 100%;
    display: block;
    margin-bottom: 10px;
    font-size: 18px;
    color: #2e276a; }
  .side-widget .address {
    width: 100%;
    display: block;
    margin-bottom: 20px; }
    .side-widget .address a {
      display: inline-block;
      color: #fff;
      text-decoration: underline; }
      .side-widget .address a:hover {
        text-decoration: none; }
  .side-widget .social-media {
    width: 100%;
    display: block;
    margin: 0;
    padding: 0; }
    .side-widget .social-media li {
      display: inline-block;
      margin-right: 20px;
      padding: 0;
      list-style: none; }
      .side-widget .social-media li a {
        color: #fff;
        font-size: 13px;
        font-weight: 600; }
        .side-widget .social-media li a:hover {
          color: #2e276a; }
  .side-widget .custom-menu {
    width: 100%;
    display: block;
    margin-bottom: 20px;
    margin-top: 20px; }
    .side-widget .custom-menu ul {
      width: 100%;
      display: block;
      margin: 0;
      padding: 0; }
      .side-widget .custom-menu ul li {
        display: inline-block;
        margin: 3px 0;
        margin-right: 10px;
        padding: 0;
        list-style: none; }
        .side-widget .custom-menu ul li ul {
          display: none;
          padding-left: 20px;
          margin-bottom: 10px; }
        .side-widget .custom-menu ul li a {
          color: #fff;
          font-size: 18px;
          font-weight: 600; }
          .side-widget .custom-menu ul li a:hover {
            text-decoration: none;
            color: #2e276a; }
  .side-widget .site-menu {
    width: 100%;
    display: block;
    margin-bottom: 20px;
    margin-top: 20px; }
    .side-widget .site-menu ul {
      width: 100%;
      display: block;
      margin: 0;
      padding: 0; }
      .side-widget .site-menu ul li {
        display: block;
        margin: 3px 0;
        padding: 0;
        list-style: none; }
        .side-widget .site-menu ul li ul {
          display: none;
          padding-left: 20px;
          margin-bottom: 10px; }
        .side-widget .site-menu ul li a {
          color: #fff;
          font-size: 18px;
          font-weight: 600; }
          .side-widget .site-menu ul li a:hover {
            text-decoration: none;
            color: #2e276a; }
  .side-widget small {
    font-size: 13px;
    width: 100%;
    display: block;
    margin-top: 20px;
    font-family: 'Barlow', sans-serif; }
  .side-widget.active {
    left: 0;
    z-index: 8888; }

/* TOPBAR */
.topbar {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  background: #2e276a;
  padding: 10px 0;
  color: #fff;
  display: none; }
  .topbar div {
    display: inline-block;
    font-size: 16px;
    font-family: 'Barlow', sans-serif; }
    .topbar div b {
      font-weight: 500;
      display: inline-block;
      margin-right: 6px;
      opacity: 0.5; }
    .topbar div a {
      display: inline-block;
      color: #fff; }

/* NAVBAR */
.navbar {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 5;
  position: fixed;
  background: #2e276a; }
  .navbar .logo {
    margin-right: auto;
    padding: 30px 0;
    padding-right: 30px; }
    .navbar .logo a {
      display: inline-block; }
      .navbar .logo a img {
        height: 75px; }
  .navbar .site-menu {
    margin: 0 auto; }
    .navbar .site-menu ul {
      display: flex;
      flex-wrap: wrap;
      margin: 0;
      padding: 0; }
      .navbar .site-menu ul li {
        display: inline-block;
        margin: 0;
        padding: 0 15px;
        list-style: none;
        -webkit-transition: all .35s ease-in-out;
        -moz-transition: all .35s ease-in-out;
        -ms-transition: all .35s ease-in-out;
        -o-transition: all .35s ease-in-out;
        transition: all .35s ease-in-out; }
        .navbar .site-menu ul li a {
          color: #fff;
          font-weight: 500;
          font-size: 17px; }
          .navbar .site-menu ul li a:hover {
            text-decoration: none; }
  .navbar .hamburger-menu {
    margin-left: auto; }
  .navbar .navbar-button {
    margin-left: 30px; }
    .navbar .navbar-button a {
      height: 70px;
      line-height: 70px;
      display: inline-block;
      background: #fff;
      color: #2e276a;
      padding: 0 21px;
      position: relative;
      font-size: 17px;
      border-radius: 55px;
      font-weight: 600; }
      .navbar .navbar-button a:before {
        content: "";
        width: 0;
        height: 100%;
        background: #000;
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0.05;
        transition-duration: 1s;
        -webkit-transition-duration: 1s;
        transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
        -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1); }
      .navbar .navbar-button a:hover {
        text-decoration: none;
        color: #2e276a; }
      .navbar .navbar-button a:hover:before {
        width: 100%; }

/* SLIDER */
.slider {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  position: relative; }
  .slider .main-slider {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    overflow: hidden; }
    .slider .main-slider .swiper-slide {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      padding: 0 15%;
      padding-top: 150px;
      background: #000; }
      .slider .main-slider .swiper-slide .slide-image {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        background-size: cover !important;
        background-position: center !important;
        opacity: 0.7; }
        .slider .main-slider .swiper-slide .slide-image:after {
          content: "";
          width: 100%;
          height: 100%;
          position: absolute;
          left: 0;
          top: 0;
          background: black;
          background: -moz-linear-gradient(351deg, rgba(0, 0, 0, 0.0018382353) 0%, black 100%);
          background: -webkit-linear-gradient(351deg, rgba(0, 0, 0, 0.0018382353) 0%, black 100%);
          background: linear-gradient(351deg, rgba(0, 0, 0, 0.0018382353) 0%, black 100%);
          filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#000000",endColorstr="#000000",GradientType=1);
          opacity: 0.4; }
      .slider .main-slider .swiper-slide .container {
        color: #fff;
        position: relative;
        z-index: 1; }
        .slider .main-slider .swiper-slide .container h1 {
          width: 100%;
          display: block;
          font-size: 60px;
          margin-bottom: 10px;
          font-weight: 800; }
        .slider .main-slider .swiper-slide .container p {
          width: 100%;
          display: block;
          color: #fff;
          margin-bottom: 50px;
          font-size: 20px; }
        .slider .main-slider .swiper-slide .container a {
          height: 70px;
          line-height: 70px;
          display: inline-block;
          padding: 0 50px;
          background: #2e276a;
          color: #fff;
          -webkit-transition: all .35s ease-in-out;
          -moz-transition: all .35s ease-in-out;
          -ms-transition: all .35s ease-in-out;
          -o-transition: all .35s ease-in-out;
          transition: all .35s ease-in-out;
          position: relative;
          font-size: 16px;
          font-weight: 600; }
          .slider .main-slider .swiper-slide .container a:before {
            content: "";
            width: 0;
            height: 100%;
            background: #000;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0.05;
            transition-duration: 1s;
            -webkit-transition-duration: 1s;
            transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
            -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1); }
          .slider .main-slider .swiper-slide .container a:hover {
            text-decoration: none;
            color: #fff; }
          .slider .main-slider .swiper-slide .container a:hover:before {
            width: 100%; }
  .slider .button-prev {
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    position: absolute;
    left: 50px;
    top: 50%;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    z-index: 3;
    font-size: 23px;
    cursor: pointer;
    -webkit-transition: all .35s ease-in-out;
    -moz-transition: all .35s ease-in-out;
    -ms-transition: all .35s ease-in-out;
    -o-transition: all .35s ease-in-out;
    transition: all .35s ease-in-out; }
    .slider .button-prev:hover {
      background: #2e276a;
      border-color: transparent; }
  .slider .button-next {
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    position: absolute;
    right: 50px;
    top: 50%;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    z-index: 3;
    font-size: 23px;
    cursor: pointer;
    -webkit-transition: all .35s ease-in-out;
    -moz-transition: all .35s ease-in-out;
    -ms-transition: all .35s ease-in-out;
    -o-transition: all .35s ease-in-out;
    transition: all .35s ease-in-out; }
    .slider .button-next:hover {
      background: #2e276a;
      border-color: transparent; }

/* PAGE HEADER */
.page-header {
  width: 100%;
  height: 926px;
  max-height: 105vh;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  position: relative;
  background-size: cover !important;
  padding-top: 1px; }
  .page-header:after {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0; }
  .page-header .container {
    position: relative;
    z-index: 1;
    color: #fff; }
    .page-header .container h2 {
      width: 100%;
      display: block;
      font-size: 50px;
      margin-bottom: 0;
      line-height: 6rem; }
    .page-header .container p {
      width: 100%;
      display: block;
      margin-bottom: 50px;
      font-size: 18px; }

/* CONTENT SECTION */
.content-section {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 100px 0;
  position: relative;
  background: #2e276a; }
  .content-section.left-white-spacing {
    position: relative; }
    .content-section.left-white-spacing .container {
      position: relative;
      z-index: 1; }
    .content-section.left-white-spacing:before {
      content: "";
      width: 30%;
      height: 100%;
      background: #fff;
      position: absolute;
      left: 0;
      top: 0; }
  .content-section.bottom-dark-spacing {
    position: relative;
    padding-bottom: 0 !important; }
    .content-section.bottom-dark-spacing .container {
      position: relative;
      z-index: 1; }
    .content-section.bottom-dark-spacing:before {
      content: "";
      width: 30%;
      height: 100%;
      background: #fff;
      position: absolute;
      left: 0;
      top: 0; }
    .content-section.bottom-dark-spacing:after {
      content: "";
      width: 100%;
      height: 140px;
      background: #2e276a;
      position: absolute;
      left: 0;
      bottom: 0; }

/* SECTION TITLE */
.section-title {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 50px;
  text-align: center; }
  .section-title figure {
    width: 100%;
    display: block;
    margin-bottom: 15px; }
    .section-title figure img {
      height: 40px; }
  .section-title h6 {
    width: 100%;
    display: block; }
  .section-title h2 {
    width: 100%;
    display: block;
    margin-bottom: 0;
    font-size: 70px;
    color: #2e276a; }
  .section-title p {
    width: 100%;
    display: block;
    margin-bottom: 0;
    opacity: 0.7; }

/* IMAGE BOX */
.col-lg-4:nth-child(1) .image-box {
  padding-right: 30px; }

.col-lg-4:nth-child(2) .image-box {
  margin-top: 60px; }

.col-lg-4:nth-child(3) .image-box {
  padding-left: 30px; }

.image-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap; }
  .image-box figure {
    width: 100%;
    display: block;
    margin-bottom: 20px; }
    .image-box figure img {
      width: 100%; }
  .image-box .time {
    display: inline-block;
    color: #2e276a;
    margin-right: 6px;
    margin-top: 4px; }
  .image-box h6 {
    display: block;
    font-size: 32px; }

/* SIDE CONTENT */
.side-content {
  width: 100%;
  display: flex;
  flex-wrap: wrap; }
  .side-content.left {
    padding-right: 20%; }
  .side-content.right {
    padding-left: 10%; }
  .side-content.light {
    color: #fff; }
    .side-content.light h2 {
      color: #fff; }
  .side-content h2 {
    width: 100%;
    display: block;
    font-size: 50px;
    font-weight: 800;
    color: #fff; }
  .side-content h6 {
    width: 100%;
    display: block;
    font-weight: 800;
    font-size: 19px;
    color: #ffffff;
    text-align: center; }
  .side-content .custom-button {
    margin-top: 30px; }
  .side-content figure {
    width: 100%;
    display: block; }
    .side-content figure img {
      height: 100px; }
  .side-content form {
    width: 100%;
    display: flex;
    margin-top: 40px; }
    .side-content form input[type="text"] {
      border: none; }
    .side-content form button[type="submit"] {
      width: 70px;
      padding: 0;
      text-align: center;
      margin-left: -70px;
      background: none;
      color: #2e276a; }

/* SIDE IMAGE */
.side-image {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin: 0;
  position: relative; }
  .side-image.full-left {
    width: 47vw;
    float: right; }
  .side-image.full-right {
    width: 49.6vw;
    float: left; }
  .side-image img {
    width: 100%; }
  .side-image .side-timetable {
    width: 340px;
    display: flex;
    flex-wrap: wrap;
    position: absolute;
    left: -50px;
    bottom: 50px;
    background: #2e276a;
    padding: 40px;
    margin: 0;
    z-index: 1; }
    .side-image .side-timetable li {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      margin: 10px 0;
      padding: 0;
      list-style: none; }
      .side-image .side-timetable li span {
        color: #fff; }
      .side-image .side-timetable li b {
        font-weight: 400;
        margin-left: auto;
        color: #2e276a; }

/* SIDE GALLERY */
.side-gallery {
  width: calc(50vw - 15px);
  display: flex;
  flex-wrap: wrap;
  margin: 0; }
  .side-gallery figure {
    width: calc(33.33333% - 10px);
    display: inline-block;
    margin: 5px 0;
    margin-left: 10px;
    background: #2e276a;
    position: relative; }
    .side-gallery figure:before {
      content: "";
      width: 4px;
      height: 50px;
      background: #fff;
      position: absolute;
      left: calc(50% - 2px);
      top: calc(50% - 25px);
      z-index: 1;
      -webkit-transition: all .35s ease-in-out;
      -moz-transition: all .35s ease-in-out;
      -ms-transition: all .35s ease-in-out;
      -o-transition: all .35s ease-in-out;
      transition: all .35s ease-in-out;
      opacity: 0; }
    .side-gallery figure:after {
      content: "";
      width: 50px;
      height: 4px;
      background: #fff;
      position: absolute;
      left: calc(50% - 25px);
      top: calc(50% - 2px);
      z-index: 1;
      -webkit-transition: all .35s ease-in-out;
      -moz-transition: all .35s ease-in-out;
      -ms-transition: all .35s ease-in-out;
      -o-transition: all .35s ease-in-out;
      transition: all .35s ease-in-out;
      opacity: 0; }
    .side-gallery figure img {
      width: 100%;
      -webkit-transition: all .35s ease-in-out;
      -moz-transition: all .35s ease-in-out;
      -ms-transition: all .35s ease-in-out;
      -o-transition: all .35s ease-in-out;
      transition: all .35s ease-in-out; }
    .side-gallery figure:hover img {
      opacity: 0.3; }
    .side-gallery figure:hover:before {
      opacity: 1; }
    .side-gallery figure:hover:after {
      opacity: 1; }

/* SIDE MEMBER */
.side-member {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  position: relative;
  overflow: hidden;
  margin-bottom: 0; }
  .side-member img {
    width: 100%;
    display: block; }
  .side-member figcaption {
    width: 100%;
    height: 140px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background: #2e276a;
    color: #fff;
    text-align: center; }
    .side-member figcaption h5 {
      width: 100%;
      display: block;
      font-size: 50px;
      line-height: 1;
      font-weight: 500;
      margin-top: auto;
      margin-bottom: 0; }
    .side-member figcaption span {
      width: 100%;
      display: block;
      font-size: 20px;
      margin-bottom: auto; }

/* PROGRESS BAR */
.custom-progress {
  width: 80%;
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
  margin-bottom: 30px; }
  .custom-progress:last-child {
    margin-bottom: 0; }
  .custom-progress h6 {
    display: inline-block;
    font-size: 20px;
    font-weight: 500;
    margin: 0; }
  .custom-progress span {
    margin-left: auto;
    font-size: 20px;
    color: #2e276a; }
  .custom-progress .progress-bar {
    width: 100%;
    height: 5px;
    background: #eee;
    display: inline-block;
    margin-top: 10px;
    border-radius: 0;
    position: relative; }
    .custom-progress .progress-bar .progress {
      width: 0;
      height: 5px;
      background: #2e276a;
      position: absolute;
      left: 0;
      top: 0;
      transition-duration: 1s;
      -webkit-transition-duration: 1s;
      transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
      -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1); }
    .custom-progress .progress-bar.animated .one {
      width: 80%; }
    .custom-progress .progress-bar.animated .two {
      width: 67%; }
    .custom-progress .progress-bar.animated .three {
      width: 92%; }
    .custom-progress .progress-bar.animated .four {
      width: 88%; }

/* TAB WRAPPER */
.tab-wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap; }
  .tab-wrapper .tab-nav {
    width: 25%;
    margin: 0;
    padding: 0;
    padding-right: 40px;
    position: relative;
    z-index: 1; }
    .tab-wrapper .tab-nav li {
      width: 100%;
      display: block;
      margin-bottom: 10px;
      list-style: none; }
      .tab-wrapper .tab-nav li.active a {
        width: calc(100% + 60px);
        margin-right: -60px;
        background: #2e276a;
        color: #fff; }
        .tab-wrapper .tab-nav li.active a:hover {
          background: #2e276a; }
      .tab-wrapper .tab-nav li a {
        width: 100%;
        display: block;
        background: #f4f4f4;
        padding: 25px;
        font-weight: 700; }
        .tab-wrapper .tab-nav li a:hover {
          background: #f0f0f0;
          text-decoration: none; }
  .tab-wrapper .tab-item {
    width: 75%;
    display: none; }
    .tab-wrapper .tab-item.active-item {
      display: flex; }
    .tab-wrapper .tab-item .tab-inner {
      width: 100%;
      display: flex;
      position: relative;
      background: #2e276a; }
      .tab-wrapper .tab-item .tab-inner ul {
        width: calc(350px - 100px);
        height: 40vw;
        overflow: auto;
        float: left;
        color: #fff;
        margin: 40px;
        margin-left: 60px;
        padding: 0; }
        .tab-wrapper .tab-item .tab-inner ul li {
          width: 100%;
          display: block;
          margin-bottom: 20px;
          padding: 0;
          list-style: none; }
          .tab-wrapper .tab-item .tab-inner ul li span {
            width: 100%;
            display: block;
            color: #2e276a; }
          .tab-wrapper .tab-item .tab-inner ul li h6 {
            width: 100%;
            display: block; }
          .tab-wrapper .tab-item .tab-inner ul li small {
            width: 100%;
            display: block;
            font-family: 'Barlow', sans-serif;
            opacity: 0.7; }
      .tab-wrapper .tab-item .tab-inner figure {
        width: 100%;
        float: left;
        margin: 0; }
        .tab-wrapper .tab-item .tab-inner figure img {
          width: 100%; }

/* COUNTER BOX */
.counter-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 40px;
  text-align: center;
  background: #fff;
  box-shadow: 0 0 40px rgba(0, 0, 0, 0.05); }
  .counter-box figure {
    width: 100%;
    display: block;
    margin-bottom: 15px; }
    .counter-box figure img {
      height: 109px; }
  .counter-box .odometer {
    line-height: 1;
    margin: 0 auto;
    font-size: 100px;
    color: #000;
    font-weight: 700; }
  .counter-box h6 {
    width: 100%;
    height: 26px;
    line-height: 26px;
    display: block;
    font-size: 22px;
    margin-bottom: 0;
    margin-top: 20px;
    color: #000;
    position: relative;
    font-weight: 700; }
    .counter-box h6:after {
      content: "";
      width: 100px;
      height: 6px;
      background: #2e276a;
      position: absolute;
      left: calc(50% - 50px);
      bottom: -40px; }

/* SERVICE BOX */
.service-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  position: relative;
  margin: 15px 0;
  background: #2e276a; }
  .service-box:before {
    content: "";
    width: 100%;
    height: 100%;
    background: #2e276a;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 0;
    -webkit-transition: all .35s ease-in-out;
    -moz-transition: all .35s ease-in-out;
    -ms-transition: all .35s ease-in-out;
    -o-transition: all .35s ease-in-out;
    transition: all .35s ease-in-out; }
  .service-box:hover img {
    opacity: 0;
    transform: scale(1.1); }
  .service-box:hover figcaption p {
    margin-bottom: 30px;
    margin-top: 10px;
    opacity: 0.7; }
  .service-box:hover figcaption a {
    margin-bottom: 0;
    opacity: 1; }
  .service-box:hover:before {
    transform: scale(1.1); }
  .service-box * {
    -webkit-transition: all .35s ease-in-out;
    -moz-transition: all .35s ease-in-out;
    -ms-transition: all .35s ease-in-out;
    -o-transition: all .35s ease-in-out;
    transition: all .35s ease-in-out; }
  .service-box img {
    width: 100%;
    position: relative; }
  .service-box figcaption {
    width: 100%;
    position: absolute;
    left: 0;
    top: 50%;
    z-index: 1;
    transform: translateY(-50%);
    color: #fff;
    text-align: center; }
    .service-box figcaption h6 {
      width: 100%;
      display: block;
      font-size: 36px;
      font-weight: 700;
      margin: 0;
      line-height: 1; }
    .service-box figcaption p {
      width: 100%;
      display: block;
      padding: 0 10%;
      margin-bottom: -100px;
      opacity: 0; }
    .service-box figcaption a {
      height: 70px;
      line-height: 68px;
      display: inline-block;
      border: 2px solid #fff;
      color: #fff;
      margin-bottom: -100px;
      opacity: 0;
      padding: 0 50px; }
      .service-box figcaption a:hover {
        text-decoration: none;
        background: #fff;
        color: #2e276a; }

/* IMAGE OVERLAP BOX */
.image-overlap-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  overflow: hidden; }
  .image-overlap-box * {
    transition-duration: 1s;
    -webkit-transition-duration: 1s;
    transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
    -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1); }
  .image-overlap-box:hover figure img {
    opacity: 0.3;
    transform: scale(1.05); }
  .image-overlap-box:hover .content img {
    margin-top: 0;
    margin-bottom: -100px;
    opacity: 0.2;
    transform: scale(1.4); }
  .image-overlap-box:hover .content p {
    margin-bottom: 40px;
    opacity: 1; }
  .image-overlap-box:hover .content a {
    margin-bottom: 0;
    opacity: 1; }
  .image-overlap-box figure {
    width: 100%;
    display: block;
    margin: 0;
    background: #2e276a; }
    .image-overlap-box figure img {
      width: 100%; }
  .image-overlap-box .content {
    width: 100%;
    position: absolute;
    left: 0;
    top: 50%;
    text-align: center;
    transform: translateY(-50%); }
    .image-overlap-box .content img {
      height: 80px;
      display: inline-block;
      margin-bottom: 20px;
      margin-top: 100px; }
    .image-overlap-box .content h6 {
      width: 100%;
      display: block;
      font-size: 30px;
      font-weight: 500;
      color: #fff; }
    .image-overlap-box .content p {
      width: 100%;
      display: block;
      padding: 0 10%;
      color: #fff;
      margin-bottom: 0;
      opacity: 0; }
    .image-overlap-box .content a {
      height: 70px;
      line-height: 68px;
      display: inline-block;
      border: 2px solid #fff;
      color: #fff;
      margin-bottom: -100px;
      opacity: 0;
      padding: 0 50px; }
      .image-overlap-box .content a:hover {
        text-decoration: none;
        background: #fff;
        color: #2e276a; }

/* CUSTOM LIST */
.custom-list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin-bottom: 20px; }
  .custom-list li {
    width: 100%;
    display: block;
    margin-bottom: 15px;
    padding: 0;
    list-style: none;
    font-family: 'Barlow', sans-serif;
    font-size: 20px; }
    .custom-list li:last-child {
      margin-bottom: 0; }
    .custom-list li:before {
      font-size: 16px;
      display: inline-block;
      margin-right: 12px; }

/* VIDEO */
.video {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  overflow: hidden;
  background: #2e276a; }
  .video img {
    width: 100%;
    -webkit-transition: all .35s ease-in-out;
    -moz-transition: all .35s ease-in-out;
    -ms-transition: all .35s ease-in-out;
    -o-transition: all .35s ease-in-out;
    transition: all .35s ease-in-out; }
  .video:hover img {
    opacity: 0.8; }
  .video a {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 50%;
    position: absolute;
    left: calc(50% - 60px);
    top: calc(50% - 60px);
    color: #2e276a;
    font-size: 30px; }
    .video a:hover {
      text-decoration: none;
      transform: scale(1.1); }

/* CAROUSEL CLASSES */
.carousel-classes {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  overflow: hidden;
  padding-bottom: 50px; }
  .carousel-classes .swiper-pagination {
    bottom: 0; }

/* ALL CLASSES */
.all-classes {
  width: calc(100% + 30px);
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
  margin-bottom: 100px;
  padding: 0; }
  .all-classes li {
    width: 33.33333%;
    margin: 0;
    margin-top: 50px;
    padding: 0 15px;
    list-style: none; }
    .all-classes li:nth-child(1), .all-classes li:nth-child(2), .all-classes li:nth-child(3) {
      margin-top: 0; }
    .all-classes li:nth-child(3n+2) {
      transform: translateY(100px); }

/* CLASS BOX */
.class-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  text-align: center; }
  .class-box:hover figure img {
    opacity: 0.3;
    transform: scale(1.05); }
  .class-box figure {
    width: 100%;
    display: block;
    margin-bottom: 20px;
    background: #2e276a;
    overflow: hidden; }
    .class-box figure img {
      width: 100%;
      -webkit-transition: all .35s ease-in-out;
      -moz-transition: all .35s ease-in-out;
      -ms-transition: all .35s ease-in-out;
      -o-transition: all .35s ease-in-out;
      transition: all .35s ease-in-out; }
  .class-box h6 {
    width: 100%;
    display: block;
    font-size: 42px;
    font-weight: 700;
    padding: 0 15%; }
  .class-box small {
    width: 100%;
    display: block;
    font-size: 16px;
    opacity: 0.7; }

/* IMAGE */
.image {
  width: 100%;
  display: block;
  margin-bottom: 30px; }
  .image.spacing {
    margin: 40px 0; }
  .image img {
    width: 100%; }

/* TEXT BOX */
.text-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap; }
  .text-box h3 {
    width: 100%;
    display: block;
    font-size: 36px;
    font-weight: 700; }
  .text-box h5 {
    width: 100%;
    display: block;
    color: #2e276a;
    font-weight: 500;
    margin-bottom: 0;
    font-size: 22px; }
  .text-box p {
    width: 100%;
    display: block;
    margin-bottom: 20px; }

/* PASS BOX */
.col-lg-6:nth-child(1) .pass-box {
  border-right: 1px solid rgba(255, 255, 255, 0.1); }

.pass-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  text-align: center;
  color: #fff; }
  .pass-box figure {
    width: 100%;
    display: block;
    margin-bottom: 30px; }
    .pass-box figure img {
      height: 80px; }
  .pass-box h6 {
    width: 100%;
    display: block;
    font-size: 50px;
    font-weight: 800; }
  .pass-box p {
    width: 100%;
    display: block;
    padding: 0 20%;
    margin-bottom: 0; }

/* RECENT NEWS */
.recent-news {
  width: 100%;
  display: flex;
  flex-wrap: wrap; }
  .recent-news:hover figure img {
    opacity: 0.3;
    transform: scale(1.05); }
  .recent-news figure {
    width: 100%;
    display: block;
    margin: 0;
    background: #2e276a;
    position: relative;
    overflow: hidden; }
    .recent-news figure img {
      width: 100%;
      -webkit-transition: all .35s ease-in-out;
      -moz-transition: all .35s ease-in-out;
      -ms-transition: all .35s ease-in-out;
      -o-transition: all .35s ease-in-out;
      transition: all .35s ease-in-out; }
  .recent-news .content {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    padding: 40px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-top: none; }
    .recent-news .content h3 {
      width: 100%;
      display: block;
      margin-bottom: 15px;
      font-weight: 500;
      font-size: 34px; }
      .recent-news .content h3 a {
        display: inline-block;
        color: #2e276a; }
        .recent-news .content h3 a:hover {
          color: #2e276a;
          text-decoration: none; }
    .recent-news .content p {
      width: 100%;
      display: block;
      margin-bottom: 25px;
      opacity: 0.7; }
    .recent-news .content small {
      width: 100%;
      display: block;
      font-size: 16px; }
      .recent-news .content small span {
        width: 5px;
        height: 5px;
        display: inline-block;
        border-radius: 50%;
        background: #2e276a;
        margin: 0 15px;
        transform: translateY(-3px); }

/* BLOG BOX */
.blog-box {
  width: 100%;
  display: block;
  position: relative;
  margin-bottom: 100px; }
  .blog-box:last-child {
    margin-bottom: 0; }
  .blog-box:hover .content h3 a {
    background-size: 100% 100%; }
  .blog-box figure {
    width: 100%;
    display: block;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    background: #2e276a; }
    .blog-box figure img {
      width: 100%;
      max-width: inherit; }
  .blog-box .content {
    width: 100%;
    height: 100%;
    display: block;
    background: #fff; }
    .blog-box .content small {
      display: block;
      font-size: 15px;
      opacity: 0.6;
      margin-bottom: 10px;
      text-transform: uppercase; }
    .blog-box .content h3 {
      width: 100%;
      display: block;
      margin-bottom: 20px;
      font-size: 56px;
      line-height: 1.1;
      font-weight: 800; }
      .blog-box .content h3 a {
        display: block;
        color: #2e276a;
        -webkit-transition: all .35s ease-in-out;
        -moz-transition: all .35s ease-in-out;
        -ms-transition: all .35s ease-in-out;
        -o-transition: all .35s ease-in-out;
        transition: all .35s ease-in-out; }
        .blog-box .content h3 a:hover {
          color: #2e276a;
          text-decoration: none; }
    .blog-box .content .author {
      width: 100%;
      display: block;
      margin-bottom: 0;
      font-weight: 500;
      font-size: 20px; }
      .blog-box .content .author img {
        height: 70px;
        display: inline-block;
        border-radius: 50%;
        margin-right: 15px; }
      .blog-box .content .author b {
        font-weight: 500;
        opacity: 0.6; }
    .blog-box .content h6 {
      font-size: 24px;
      line-height: 1.7;
      margin: 30px 0; }
    .blog-box .content strong {
      font-weight: 600; }
    .blog-box .content figure {
      margin: 30px 0; }
    .blog-box .content blockquote {
      width: 100%;
      display: block;
      color: #2e276a;
      font-size: 26px;
      font-family: 'Barlow', sans-serif;
      margin-bottom: 30px; }
      .blog-box .content blockquote:before {
        content: "“";
        font-size: 100px;
        height: 50px;
        line-height: 0.9;
        display: block; }
    .blog-box .content ul {
      padding-left: 20px; }
      .blog-box .content ul li {
        margin: 4px 0; }
    .blog-box .content .half-image {
      width: 50%;
      float: right;
      margin-left: 20px; }
    .blog-box .content .full-width {
      width: calc(100% + 100px);
      float: left;
      margin-left: -50px;
      margin-right: -50px; }

/* SIDEBAR */
.sidebar {
  width: 100%;
  display: block;
  padding-left: 30px; }
  .sidebar .widget {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    border: 1px solid #eee;
    padding: 40px;
    margin-bottom: 40px;
    position: relative; }
    .sidebar .widget * {
      position: relative; }
    .sidebar .widget .widget-title {
      width: 100%;
      display: block;
      position: relative;
      z-index: 1;
      font-weight: 800;
      letter-spacing: 1px;
      font-size: 22px;
      color: #2e276a;
      margin-bottom: 20px;
      padding-bottom: 20px; }
      .sidebar .widget .widget-title:before {
        content: "";
        width: 100%;
        height: 2px;
        background: #2e276a;
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: -1; }
    .sidebar .widget form {
      width: 100%;
      display: block;
      margin-top: 10px; }
      .sidebar .widget form input[type="submit"] {
        margin-top: 10px;
        background: #2e276a;
        color: #fff; }
    .sidebar .widget .categories {
      width: 100%;
      display: block;
      margin: 0;
      padding: 0; }
      .sidebar .widget .categories li {
        width: 100%;
        display: block;
        margin: 4px 0;
        padding: 0;
        list-style: none; }
        .sidebar .widget .categories li a {
          color: #2e276a;
          font-size: 19px; }
    .sidebar .widget .side-gallery {
      width: calc(100% + 4px);
      float: left;
      margin: 0 -2px;
      padding: 0; }
      .sidebar .widget .side-gallery li {
        width: 50%;
        float: left;
        margin: 0;
        padding: 2px;
        list-style: none; }

/* BRANCH BOX */
.branch-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap; }
  .branch-box h6 {
    width: 100%;
    display: block;
    font-weight: 800;
    font-size: 24px;
    color: #2e276a; }
  .branch-box address {
    width: 100%;
    display: block;
    margin-bottom: 10px; }
    .branch-box address b {
      width: 100%;
      display: block;
      margin-top: 5px;
      font-weight: 500; }
  .branch-box a {
    display: inline-block;
    text-decoration: underline; }
    .branch-box a:hover {
      text-decoration: none;
      color: #2e276a; }

/* MEMBER BOX */
.member-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  position: relative;
  overflow: hidden;
  margin: 0; }
  .member-box:hover figcaption {
    bottom: 0;
    transform: translateY(0); }
  .member-box img {
    width: 100%;
    display: block; }
  .member-box figcaption {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    position: absolute;
    left: 0;
    bottom: 120px;
    color: #fff;
    transform: translateY(100%);
    background: #2e276a;
    text-align: center;
    padding: 30px;
    -webkit-transition: all .35s ease-in-out;
    -moz-transition: all .35s ease-in-out;
    -ms-transition: all .35s ease-in-out;
    -o-transition: all .35s ease-in-out;
    transition: all .35s ease-in-out; }
    .member-box figcaption h6 {
      width: 100%;
      display: block;
      font-size: 34px;
      font-weight: 700; }
    .member-box figcaption small {
      width: 100%;
      display: block;
      margin-bottom: 15px; }
    .member-box figcaption p {
      width: 100%;
      display: block;
      padding: 0 10%;
      opacity: 0.7; }
    .member-box figcaption ul {
      width: 100%;
      display: flex;
      justify-content: center;
      margin: 0;
      padding: 0; }
      .member-box figcaption ul li {
        display: inline-block;
        margin: 0 7px;
        padding: 0;
        list-style: none; }
        .member-box figcaption ul li a {
          color: #fff;
          float: left;
          font-size: 13px; }

/* CTA BOX */
.cta-box {
  width: 100%;
  max-width: 600px;
  display: inline-block;
  margin: 0 auto;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 50px;
  text-align: center; }
  .cta-box h2 {
    width: 100%;
    display: block;
    font-size: 50px;
    font-weight: 500; }
  .cta-box p {
    width: 100%; }
  .cta-box .custom-button {
    margin-top: 10px !important; }

/* TESTIMONIALS */
.testimonial {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  text-align: center; }
  .testimonial blockquote {
    width: 100%;
    display: block;
    font-size: 32px;
    font-family: 'Barlow', sans-serif; }
  .testimonial p {
    width: 100%;
    display: block; }
  .testimonial i {
    display: inline-block;
    margin: 0 3px;
    color: #2e276a; }
  .testimonial h6 {
    width: 100%;
    display: block;
    margin: 0; }
  .testimonial figure {
    width: 100%;
    display: block;
    margin: 0; }
    .testimonial figure img {
      height: 440px; }

/* CONTACT BOX */
.contact-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  font-family: 'Barlow', sans-serif; }
  .contact-box li {
    width: 100%;
    display: flex;
    margin-bottom: 15px;
    padding: 0;
    list-style: none;
    line-height: 1.2; }
    .contact-box li:last-child {
      margin-bottom: 0; }
    .contact-box li h6 {
      width: 100px;
      display: inline-block;
      color: #2e276a;
      margin: 0; }
    .contact-box li span {
      display: inline-block; }
    .contact-box li a {
      display: inline-block;
      text-decoration: underline; }

/* CONTACT FORM */
.contact-form {
  width: 100%;
  display: block;
  margin: 0;
  padding: 0; }
  .contact-form .form-group {
    width: 100%;
    display: block; }
    .contact-form .form-group:last-child {
      margin-bottom: 0; }

/* GOOGLE MAPS */
.google-maps {
  width: 100%;
  display: block;
  position: relative; }
  .google-maps iframe {
    width: 100%;
    height: 500px;
    display: block;
    border: none;
    filter: grayscale(1);
    position: relative;
    z-index: 0; }
  .google-maps .timetable {
    width: 340px;
    display: flex;
    flex-wrap: wrap;
    position: absolute;
    left: 100px;
    top: 50%;
    transform: translateY(-50%);
    background: #2e276a;
    padding: 40px;
    margin: 0;
    z-index: 1; }
    .google-maps .timetable li {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      margin: 10px 0;
      padding: 0;
      list-style: none; }
      .google-maps .timetable li span {
        color: #fff; }
      .google-maps .timetable li b {
        font-weight: 400;
        margin-left: auto;
        color: #2e276a; }

/* icon */
.icon {
  width: 90px;
  height: 93px;
  line-height: 80px;
  border-radius: 5px;
  color: #2e276a;
  font-size: 50px;
  -webkit-transition: .5s;
  transition: .5s;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 15px;
  font-size: 7rem; }

.icon:hover {
  color: #000; }

.iconp {
  font-size: 3.5rem;
  float: left;
  margin-top: 0.5rem; }

/*icon white*/
.iconw {
  width: 90px;
  height: 93px;
  line-height: 80px;
  border-radius: 5px;
  color: #fff;
  font-size: 50px;
  -webkit-transition: .5s;
  transition: .5s;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 15px;
  font-size: 7rem; }

.iconw:hover {
  color: #ebebeb; }

/* PAGINATION */
.pagination {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin: 0; }
  .pagination .page-item {
    display: inline-block; }
    .pagination .page-item .page-link {
      height: 60px;
      line-height: 60px;
      padding: 0 40px;
      border-radius: 0 !important;
      font-weight: 500;
      color: #2e276a;
      outline: none !important; }
      .pagination .page-item .page-link:focus {
        outline: none !important; }

/* FOOTER */
.footer {
  width: 100%;
  flex-wrap: wrap;
  padding: 100px 0;
  background: #2e276a;
  position: relative;
  color: #fff; }
  .footer .logo {
    width: 100%;
    display: block;
    margin-bottom: 30px; }
    .footer .logo img {
      width: auto;
      height: 50px; }
  .footer .footer-info {
    width: 100%;
    display: block;
    margin-bottom: 20px;
    font-family: 'Barlow', sans-serif; }
    .footer .footer-info a {
      color: #fff;
      text-decoration: underline;
      color: #2e276a;
      font-size: 14px; }
  .footer .copyright {
    width: 100%;
    display: block;
    margin: 0;
    font-size: 14px; }
  .footer .footer-social {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-top: -37px;
    padding: 0;
    font-size: 2rem; }
    .footer .footer-social li {
      display: inline-block;
      margin-right: 10px;
      padding: 0;
      list-style: none; }
      .footer .footer-social li a {
        width: 40px;
        height: 40px;
        line-height: 40px;
        display: inline-block;
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: #fff;
        text-align: center;
        font-size: 13px; }
        .footer .footer-social li a:hover {
          background: #2e276a;
          border-color: transparent; }
  .footer .widget-title {
    width: 100%;
    display: block;
    font-weight: 500;
    font-size: 26px;
    margin-top: 10px;
    margin-bottom: 15px; }
  .footer .footer-menu {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin: 0;
    padding: 0; }
    .footer .footer-menu li {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin: 0;
      padding: 5px 0;
      list-style: none; }
      .footer .footer-menu li:before {
        content: "";
        width: 4px;
        height: 4px;
        display: inline-block;
        background: #2e276a;
        border-radius: 50%;
        margin-right: 9px; }
      .footer .footer-menu li a {
        color: #fff; }

/* RESPONSIVE MEDIUM  */
@media only screen and (max-width: 1199px), only screen and (max-device-width: 1199px) {
  .page-header {
    width: 100%;
    height: 926px;
    max-height: 201vh;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    position: relative;
    background-size: cover !important;
    padding-top: 1px; }

  .col-lg-4:nth-child(1) .image-box {
    padding-right: 0; }

  .col-lg-4:nth-child(3) .image-box {
    padding-left: 0; }

  .side-content h2 {
    font-size: 60px; }

  .side-content h2 br {
    display: none; }

  .counter-box {
    padding: 30px; }

  .side-content figure img {
    height: 70px; }

  .carousel-classes h6 {
    font-size: 38px; }

  .class-box h6 {
    font-size: 38px; }

  .sidebar {
    padding-left: 0; }

  .sidebar .widget {
    padding: 30px;
    margin-bottom: 30px; }

  .blog-box .content .full-width {
    width: 100%;
    margin-left: 0;
    margin-right: 0; }

  .recent-news .content {
    padding: 30px; }

  .footer .footer-menu li a {
    font-size: 17px; } }
/* RESPONSIVE TABLET  */
@media only screen and (max-width: 991px), only screen and (max-device-width: 991px) {
  .page-header {
    width: 100%;
    height: 926px;
    max-height: 201vh;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    position: relative;
    background-size: cover !important;
    padding-top: 1px; }

  .side-widget .hide-mobile {
    display: none; }

  .side-widget .show-mobile {
    display: flex; }

  .side-widget .site-menu ul li {
    opacity: 1 !important; }

  .side-widget .site-menu ul li a {
    font-size: 22px; }

  .navbar .site-menu {
    display: none; }

  .slider .button-prev {
    display: none; }

  .slider .button-next {
    display: none; }

  .slider .main-slider .swiper-slide .container h1 {
    font-size: 60px; }

  .slider .main-slider .swiper-slide .container h1 br {
    display: none; }

  .col-lg-4:nth-child(2) .image-box {
    margin-top: 0;
    margin-bottom: 50px; }

  .no-spacing .side-content {
    padding: 100px 0 !important; }

  .side-image.full-right {
    width: 100%; }

  .side-image.full-left {
    width: 100%; }

  .side-image .side-timetable {
    width: 100%;
    position: static; }

  .col-lg-4:nth-child(3) .counter-box {
    margin-top: 50px; }

  .carousel-classes h6 {
    font-size: 30px; }

  .content-section.bottom-dark-spacing:after {
    display: none; }

  .col-lg-6:nth-child(1) .pass-box {
    border-right: 0;
    margin-bottom: 50px; }

  .col-lg-4:nth-child(3) .recent-news {
    margin-top: 30px; }

  .side-gallery {
    width: calc(100% + 10px);
    margin-left: -5px;
    margin-right: -5px; }

  .side-gallery figure {
    margin: 5px !important; }

  .col-lg-3:nth-child(1) .branch-box {
    margin-bottom: 50px; }

  .col-lg-3:nth-child(2) .branch-box {
    margin-bottom: 50px; }

  .col-lg-4:nth-child(4) .member-box {
    margin-top: 30px; }

  .tab-wrapper .tab-nav {
    width: 100%;
    display: flex;
    padding: 0;
    justify-content: space-between; }

  .tab-wrapper .tab-nav li {
    width: auto;
    flex: 1;
    display: inline-block; }

  .tab-wrapper .tab-nav li.active a {
    width: 100%;
    margin-right: 0; }

  .tab-wrapper .tab-item {
    width: 100%; }

  .tab-wrapper .tab-item .tab-inner ul {
    height: 50vw; }

  .section-title h2 {
    font-size: 54px; }

  .section-title h2 br {
    display: none; }

  .side-content.left {
    padding-right: 0; }

  .all-classes {
    margin-bottom: 0; }

  .all-classes li {
    width: 50%; }

  .all-classes li:nth-child(3n+2) {
    transform: none; }

  .all-classes li:nth-child(3) {
    margin-top: 50px; }

  .sidebar {
    margin-top: 50px; }

  .sidebar .widget .side-gallery li {
    width: 33.33333%; }

  .contact-box {
    margin-bottom: 50px; }

  .footer .copyright {
    margin-top: 40px; }

  .footer .widget-title {
    margin-top: 50px; } }
/* RESPONSIVE MOBILE */
@media only screen and (max-width: 767px), only screen and (max-device-width: 767px) {
  .side-widget {
    max-width: 80vw; }

  .topbar div b {
    display: none; }

  .topbar div {
    font-size: 14px; }

  .navbar .navbar-button {
    display: none; }

  .slider .main-slider .swiper-slide {
    padding: 0;
    padding-top: 100px; }

  .page-header {
    width: 100%;
    height: 926px;
    max-height: 201vh;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    position: relative;
    background-size: cover !important;
    padding-top: 1px; }

  .footer .footer-social {
    width: 100%;
    padding: 0;
    text-align: center;
    align-items: center;
    clear: both;
    margin: auto;
    font-size: 5rem;
    width: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap; }

  .iconw {
    width: 90px;
    height: 93px;
    line-height: 80px;
    border-radius: 5px;
    color: #fff;
    font-size: 50px;
    -webkit-transition: .5s;
    transition: .5s;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 15px;
    font-size: 18rem;
    text-align: center;
    align-items: center;
    clear: both;
    margin: auto;
    padding: 0;
    text-align: center;
    align-items: center;
    clear: both;
    margin: auto;
    width: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap; }

  .icon {
    width: 90px;
    height: 93px;
    line-height: 80px;
    border-radius: 5px;
    color: #2e276a;
    font-size: 50px;
    -webkit-transition: .5s;
    transition: .5s;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 15px;
    font-size: 18rem;
    text-align: center;
    align-items: center;
    clear: both;
    margin: auto;
    padding: 0;
    text-align: center;
    align-items: center;
    clear: both;
    margin: auto;
    width: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap; }

  .page-header .container p {
    width: 100%;
    display: block;
    margin-bottom: 50px;
    font-size: 16px; }

  .slider .main-slider .swiper-slide .container h1 {
    font-size: 40px; }

  .section-title h2 {
    font-size: 42px; }

  .page-header .container {
    padding-top: 50px; }

  .page-header .container h2 {
    font-size: 50px;
    font-size: 27px;
    line-height: 5rem; }

  .col-lg-4:nth-child(1) .image-box {
    margin-bottom: 50px; }

  .side-content h2 {
    font-size: 42px; }

  .side-image .side-timetable {
    padding: 30px; }

  .col-lg-4:nth-child(2) .counter-box {
    margin-top: 30px; }

  .side-member figcaption h5 {
    font-size: 40px; }

  .pass-box h6 {
    font-size: 38px; }

  .pass-box p {
    padding: 0; }

  .video a {
    transform: scale(0.7); }

  .video a:hover {
    transform: scale(0.8); }

  .pagination .page-item .page-link {
    padding: 0 30px; }

  .google-maps iframe {
    display: flex; }

  .google-maps .timetable {
    width: 100%;
    position: static;
    padding: 30px;
    margin-bottom: -100px; }

  .blog-box .content h3 {
    font-size: 44px; }

  .all-classes li:nth-child(2) {
    margin-top: 50px; }

  .all-classes li {
    width: 100%; }

  .class-box h6 {
    padding: 0;
    font-size: 32px; }

  .tab-wrapper .tab-nav {
    max-width: 100%;
    overflow-x: auto; }

  .tab-wrapper .tab-item .tab-inner {
    flex-wrap: wrap; }

  .tab-wrapper .tab-item .tab-inner ul {
    width: 100%;
    height: 300px;
    margin: 30px 0;
    padding: 30px; }

  .tab-wrapper .tab-item .tab-inner figure {
    width: 100%; }

  .cta-box {
    padding: 30px; }

  .cta-box h2 {
    font-size: 44px; }

  .cta-box .custom-button {
    padding: 0;
    text-align: center;
    width: 100%; }

  .testimonial figure img {
    height: auto; }

  .col-lg-4:nth-child(3) .member-box {
    margin-top: 30px; }

  .col-lg-4:nth-child(2) .recent-news {
    margin-top: 30px; }

  .side-gallery {
    margin-top: 100px; }

  .side-gallery figure {
    width: calc(50% - 10px); }

  .col-lg-3:nth-child(3) .branch-box {
    margin-bottom: 50px; } }
@media (min-width: 767px) and (max-width: 850px) {
  .footer .footer-social {
    width: 100%;
    padding: 0;
    text-align: center;
    align-items: center;
    clear: both;
    margin: auto;
    font-size: 5rem;
    width: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap; }

  .iconw {
    width: 90px;
    height: 93px;
    line-height: 80px;
    border-radius: 5px;
    color: #fff;
    font-size: 50px;
    -webkit-transition: .5s;
    transition: .5s;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 15px;
    font-size: 18rem;
    text-align: center;
    align-items: center;
    clear: both;
    margin: auto;
    padding: 0;
    text-align: center;
    align-items: center;
    clear: both;
    margin: auto;
    width: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap; }

  .icon {
    width: 90px;
    height: 93px;
    line-height: 80px;
    border-radius: 5px;
    color: #2e276a;
    font-size: 50px;
    -webkit-transition: .5s;
    transition: .5s;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 15px;
    font-size: 18rem;
    text-align: center;
    align-items: center;
    clear: both;
    margin: auto;
    padding: 0;
    text-align: center;
    align-items: center;
    clear: both;
    margin: auto;
    width: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap; }

  .baslik-3white {
    margin-top: 10rem; } }
@media only screen and (min-width: 1180px) and (max-width: 1180px) {
  .side-widget .show-mobile {
    display: block; }

  .navbar .site-menu {
    margin: 0 auto;
    display: none; }

  .side-widget .hide-mobile {
    width: 100%;
    display: inline-block;
    display: none; } }
.services-kutu2 {
  border-radius: 55px;
  border: 2px solid #fff; }

.services-kutu2.tt {
  background: #f7f7f7;
  border-radius: 55px;
  border-bottom: 5px solid #2e276a; }

.loadwraps {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
  pointer-events: none;
  background: #fff;
  transition: all 1s; }
  .loadwraps:before, .loadwraps:after,
  .loadwraps .dot,
  .loadwraps .outline {
    position: absolute;
    top: 50%;
    left: 50%;
    -o-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    border-radius: 50%; }
  .loadwraps .dot {
    width: 96px;
    height: 96px;
    background: #FFFFFF;
    -o-animation: in 4s linear infinite;
    -moz-animation: in 4s linear infinite;
    -ms-animation: in 4s linear infinite;
    -webkit-animation: in 4s linear infinite;
    animation: in 4s linear infinite;
    z-index: 2; }
  .loadwraps:before {
    content: "";
    width: 0px;
    height: 0px;
    background: #2e276a;
    -o-animation: out1 4s linear infinite;
    -moz-animation: out1 4s linear infinite;
    -ms-animation: out1 4s linear infinite;
    -webkit-animation: out1 4s linear infinite;
    animation: out1 4s linear infinite; }
  .loadwraps:after {
    content: "";
    width: 0px;
    height: 0px;
    background: #FFFFFF;
    -o-animation: out2 4s linear infinite;
    -moz-animation: out2 4s linear infinite;
    -ms-animation: out2 4s linear infinite;
    -webkit-animation: out2 4s linear infinite;
    animation: out2 4s linear infinite; }
  .loadwraps .outline {
    width: 120px;
    height: 120px;
    z-index: 2; }
    .loadwraps .outline span {
      width: 68px;
      height: 68px;
      -o-transform-origin: 100% 100%;
      -moz-transform-origin: 100% 100%;
      -webkit-transform-origin: 100% 100%;
      -ms-transform-origin: 100% 100%;
      transform-origin: 100% 100%;
      -o-transform: rotate(45deg) skewX(80deg);
      -moz-transform: rotate(45deg) skewX(80deg);
      -webkit-transform: rotate(45deg) skewX(80deg);
      -ms-transform: rotate(45deg) skewX(80deg);
      transform: rotate(45deg) skewX(80deg);
      overflow: hidden;
      position: absolute;
      bottom: 50%;
      right: 50%;
      -o-animation: outline 4s linear infinite;
      -moz-animation: outline 4s linear infinite;
      -ms-animation: outline 4s linear infinite;
      -webkit-animation: outline 4s linear infinite;
      animation: outline 4s linear infinite; }
      .loadwraps .outline span:before {
        content: "";
        display: block;
        border: solid 5px #fff;
        width: 200%;
        height: 200%;
        border-radius: 50%;
        -o-transform: skewX(-80deg);
        -moz-transform: skewX(-80deg);
        -webkit-transform: skewX(-80deg);
        -ms-transform: skewX(-80deg);
        transform: skewX(-80deg);
        -o-animation: outlineBefore 4s linear infinite;
        -moz-animation: outlineBefore 4s linear infinite;
        -ms-animation: outlineBefore 4s linear infinite;
        -webkit-animation: outlineBefore 4s linear infinite;
        animation: outlineBefore 4s linear infinite; }

@-o-keyframes outline {
  0% {
    -o-transform: rotate(0deg) skewX(80deg);
    -moz-transform: rotate(0deg) skewX(80deg);
    -webkit-transform: rotate(0deg) skewX(80deg);
    -ms-transform: rotate(0deg) skewX(80deg);
    transform: rotate(0deg) skewX(80deg); }
  25% {
    -o-transform: rotate(500deg) skewX(15deg);
    -moz-transform: rotate(500deg) skewX(15deg);
    -webkit-transform: rotate(500deg) skewX(15deg);
    -ms-transform: rotate(500deg) skewX(15deg);
    transform: rotate(500deg) skewX(15deg); }
  50% {
    -o-transform: rotate(1000deg) skewX(40deg);
    -moz-transform: rotate(1000deg) skewX(40deg);
    -webkit-transform: rotate(1000deg) skewX(40deg);
    -ms-transform: rotate(1000deg) skewX(40deg);
    transform: rotate(1000deg) skewX(40deg); }
  75% {
    -o-transform: rotate(1500deg) skewX(60deg);
    -moz-transform: rotate(1500deg) skewX(60deg);
    -webkit-transform: rotate(1500deg) skewX(60deg);
    -ms-transform: rotate(1500deg) skewX(60deg);
    transform: rotate(1500deg) skewX(60deg); }
  100% {
    -o-transform: rotate(2160deg) skewX(80deg);
    -moz-transform: rotate(2160deg) skewX(80deg);
    -webkit-transform: rotate(2160deg) skewX(80deg);
    -ms-transform: rotate(2160deg) skewX(80deg);
    transform: rotate(2160deg) skewX(80deg); } }
@-moz-keyframes outline {
  0% {
    -o-transform: rotate(0deg) skewX(80deg);
    -moz-transform: rotate(0deg) skewX(80deg);
    -webkit-transform: rotate(0deg) skewX(80deg);
    -ms-transform: rotate(0deg) skewX(80deg);
    transform: rotate(0deg) skewX(80deg); }
  25% {
    -o-transform: rotate(500deg) skewX(15deg);
    -moz-transform: rotate(500deg) skewX(15deg);
    -webkit-transform: rotate(500deg) skewX(15deg);
    -ms-transform: rotate(500deg) skewX(15deg);
    transform: rotate(500deg) skewX(15deg); }
  50% {
    -o-transform: rotate(1000deg) skewX(40deg);
    -moz-transform: rotate(1000deg) skewX(40deg);
    -webkit-transform: rotate(1000deg) skewX(40deg);
    -ms-transform: rotate(1000deg) skewX(40deg);
    transform: rotate(1000deg) skewX(40deg); }
  75% {
    -o-transform: rotate(1500deg) skewX(60deg);
    -moz-transform: rotate(1500deg) skewX(60deg);
    -webkit-transform: rotate(1500deg) skewX(60deg);
    -ms-transform: rotate(1500deg) skewX(60deg);
    transform: rotate(1500deg) skewX(60deg); }
  100% {
    -o-transform: rotate(2160deg) skewX(80deg);
    -moz-transform: rotate(2160deg) skewX(80deg);
    -webkit-transform: rotate(2160deg) skewX(80deg);
    -ms-transform: rotate(2160deg) skewX(80deg);
    transform: rotate(2160deg) skewX(80deg); } }
@-webkit-keyframes outline {
  0% {
    -o-transform: rotate(0deg) skewX(80deg);
    -moz-transform: rotate(0deg) skewX(80deg);
    -webkit-transform: rotate(0deg) skewX(80deg);
    -ms-transform: rotate(0deg) skewX(80deg);
    transform: rotate(0deg) skewX(80deg); }
  25% {
    -o-transform: rotate(500deg) skewX(15deg);
    -moz-transform: rotate(500deg) skewX(15deg);
    -webkit-transform: rotate(500deg) skewX(15deg);
    -ms-transform: rotate(500deg) skewX(15deg);
    transform: rotate(500deg) skewX(15deg); }
  50% {
    -o-transform: rotate(1000deg) skewX(40deg);
    -moz-transform: rotate(1000deg) skewX(40deg);
    -webkit-transform: rotate(1000deg) skewX(40deg);
    -ms-transform: rotate(1000deg) skewX(40deg);
    transform: rotate(1000deg) skewX(40deg); }
  75% {
    -o-transform: rotate(1500deg) skewX(60deg);
    -moz-transform: rotate(1500deg) skewX(60deg);
    -webkit-transform: rotate(1500deg) skewX(60deg);
    -ms-transform: rotate(1500deg) skewX(60deg);
    transform: rotate(1500deg) skewX(60deg); }
  100% {
    -o-transform: rotate(2160deg) skewX(80deg);
    -moz-transform: rotate(2160deg) skewX(80deg);
    -webkit-transform: rotate(2160deg) skewX(80deg);
    -ms-transform: rotate(2160deg) skewX(80deg);
    transform: rotate(2160deg) skewX(80deg); } }
@keyframes outline {
  0% {
    -o-transform: rotate(0deg) skewX(80deg);
    -moz-transform: rotate(0deg) skewX(80deg);
    -webkit-transform: rotate(0deg) skewX(80deg);
    -ms-transform: rotate(0deg) skewX(80deg);
    transform: rotate(0deg) skewX(80deg); }
  25% {
    -o-transform: rotate(500deg) skewX(15deg);
    -moz-transform: rotate(500deg) skewX(15deg);
    -webkit-transform: rotate(500deg) skewX(15deg);
    -ms-transform: rotate(500deg) skewX(15deg);
    transform: rotate(500deg) skewX(15deg); }
  50% {
    -o-transform: rotate(1000deg) skewX(40deg);
    -moz-transform: rotate(1000deg) skewX(40deg);
    -webkit-transform: rotate(1000deg) skewX(40deg);
    -ms-transform: rotate(1000deg) skewX(40deg);
    transform: rotate(1000deg) skewX(40deg); }
  75% {
    -o-transform: rotate(1500deg) skewX(60deg);
    -moz-transform: rotate(1500deg) skewX(60deg);
    -webkit-transform: rotate(1500deg) skewX(60deg);
    -ms-transform: rotate(1500deg) skewX(60deg);
    transform: rotate(1500deg) skewX(60deg); }
  100% {
    -o-transform: rotate(2160deg) skewX(80deg);
    -moz-transform: rotate(2160deg) skewX(80deg);
    -webkit-transform: rotate(2160deg) skewX(80deg);
    -ms-transform: rotate(2160deg) skewX(80deg);
    transform: rotate(2160deg) skewX(80deg); } }
@-o-keyframes outlineBefore {
  0% {
    -o-transform: skewX(-80deg);
    -moz-transform: skewX(-80deg);
    -webkit-transform: skewX(-80deg);
    -ms-transform: skewX(-80deg);
    transform: skewX(-80deg);
    border: solid 5px #ffffff; }
  25% {
    -o-transform: skewX(-15deg);
    -moz-transform: skewX(-15deg);
    -webkit-transform: skewX(-15deg);
    -ms-transform: skewX(-15deg);
    transform: skewX(-15deg);
    border: solid 5px #ffffff; }
  49% {
    border: solid 5px #ffffff; }
  50% {
    -o-transform: skewX(-40deg);
    -moz-transform: skewX(-40deg);
    -webkit-transform: skewX(-40deg);
    -ms-transform: skewX(-40deg);
    transform: skewX(-40deg);
    border: solid 5px #2e276a; }
  75% {
    -o-transform: skewX(-60deg);
    -moz-transform: skewX(-60deg);
    -webkit-transform: skewX(-60deg);
    -ms-transform: skewX(-60deg);
    transform: skewX(-60deg);
    border: solid 5px #2e276a; }
  100% {
    -o-transform: skewX(-80deg);
    -moz-transform: skewX(-80deg);
    -webkit-transform: skewX(-80deg);
    -ms-transform: skewX(-80deg);
    transform: skewX(-80deg);
    border: solid 5px #2e276a; } }
@-moz-keyframes outlineBefore {
  0% {
    -o-transform: skewX(-80deg);
    -moz-transform: skewX(-80deg);
    -webkit-transform: skewX(-80deg);
    -ms-transform: skewX(-80deg);
    transform: skewX(-80deg);
    border: solid 5px #ffffff; }
  25% {
    -o-transform: skewX(-15deg);
    -moz-transform: skewX(-15deg);
    -webkit-transform: skewX(-15deg);
    -ms-transform: skewX(-15deg);
    transform: skewX(-15deg);
    border: solid 5px #ffffff; }
  49% {
    border: solid 5px #ffffff; }
  50% {
    -o-transform: skewX(-40deg);
    -moz-transform: skewX(-40deg);
    -webkit-transform: skewX(-40deg);
    -ms-transform: skewX(-40deg);
    transform: skewX(-40deg);
    border: solid 5px #2e276a; }
  75% {
    -o-transform: skewX(-60deg);
    -moz-transform: skewX(-60deg);
    -webkit-transform: skewX(-60deg);
    -ms-transform: skewX(-60deg);
    transform: skewX(-60deg);
    border: solid 5px #2e276a; }
  100% {
    -o-transform: skewX(-80deg);
    -moz-transform: skewX(-80deg);
    -webkit-transform: skewX(-80deg);
    -ms-transform: skewX(-80deg);
    transform: skewX(-80deg);
    border: solid 5px #2e276a; } }
@-webkit-keyframes outlineBefore {
  0% {
    -o-transform: skewX(-80deg);
    -moz-transform: skewX(-80deg);
    -webkit-transform: skewX(-80deg);
    -ms-transform: skewX(-80deg);
    transform: skewX(-80deg);
    border: solid 5px #ffffff; }
  25% {
    -o-transform: skewX(-15deg);
    -moz-transform: skewX(-15deg);
    -webkit-transform: skewX(-15deg);
    -ms-transform: skewX(-15deg);
    transform: skewX(-15deg);
    border: solid 5px #ffffff; }
  49% {
    border: solid 5px #ffffff; }
  50% {
    -o-transform: skewX(-40deg);
    -moz-transform: skewX(-40deg);
    -webkit-transform: skewX(-40deg);
    -ms-transform: skewX(-40deg);
    transform: skewX(-40deg);
    border: solid 5px #2e276a; }
  75% {
    -o-transform: skewX(-60deg);
    -moz-transform: skewX(-60deg);
    -webkit-transform: skewX(-60deg);
    -ms-transform: skewX(-60deg);
    transform: skewX(-60deg);
    border: solid 5px #2e276a; }
  100% {
    -o-transform: skewX(-80deg);
    -moz-transform: skewX(-80deg);
    -webkit-transform: skewX(-80deg);
    -ms-transform: skewX(-80deg);
    transform: skewX(-80deg);
    border: solid 5px #2e276a; } }
@keyframes outlineBefore {
  0% {
    -o-transform: skewX(-80deg);
    -moz-transform: skewX(-80deg);
    -webkit-transform: skewX(-80deg);
    -ms-transform: skewX(-80deg);
    transform: skewX(-80deg);
    border: solid 5px #ffffff; }
  25% {
    -o-transform: skewX(-15deg);
    -moz-transform: skewX(-15deg);
    -webkit-transform: skewX(-15deg);
    -ms-transform: skewX(-15deg);
    transform: skewX(-15deg);
    border: solid 5px #ffffff; }
  49% {
    border: solid 5px #ffffff; }
  50% {
    -o-transform: skewX(-40deg);
    -moz-transform: skewX(-40deg);
    -webkit-transform: skewX(-40deg);
    -ms-transform: skewX(-40deg);
    transform: skewX(-40deg);
    border: solid 5px #2e276a; }
  75% {
    -o-transform: skewX(-60deg);
    -moz-transform: skewX(-60deg);
    -webkit-transform: skewX(-60deg);
    -ms-transform: skewX(-60deg);
    transform: skewX(-60deg);
    border: solid 5px #2e276a; }
  100% {
    -o-transform: skewX(-80deg);
    -moz-transform: skewX(-80deg);
    -webkit-transform: skewX(-80deg);
    -ms-transform: skewX(-80deg);
    transform: skewX(-80deg);
    border: solid 5px #2e276a; } }
@-o-keyframes in {
  0% {
    width: 144px;
    height: 144px;
    background: #FFFFFF; }
  40% {
    width: 0px;
    height: 0px;
    background: #FFFFFF; }
  41% {
    width: 0px;
    height: 0px;
    background: #2e276a; }
  50% {
    width: 144px;
    height: 144px;
    background: #2e276a; }
  90% {
    width: 0px;
    height: 0px;
    background: #2e276a; }
  91% {
    width: 0px;
    height: 0px;
    background: #FFFFFF; }
  100% {
    width: 144px;
    height: 144px;
    background: #FFFFFF; } }
@-moz-keyframes in {
  0% {
    width: 144px;
    height: 144px;
    background: #FFFFFF; }
  40% {
    width: 0px;
    height: 0px;
    background: #FFFFFF; }
  41% {
    width: 0px;
    height: 0px;
    background: #2e276a; }
  50% {
    width: 144px;
    height: 144px;
    background: #2e276a; }
  90% {
    width: 0px;
    height: 0px;
    background: #2e276a; }
  91% {
    width: 0px;
    height: 0px;
    background: #FFFFFF; }
  100% {
    width: 144px;
    height: 144px;
    background: #FFFFFF; } }
@-webkit-keyframes in {
  0% {
    width: 144px;
    height: 144px;
    background: #FFFFFF; }
  40% {
    width: 0px;
    height: 0px;
    background: #FFFFFF; }
  41% {
    width: 0px;
    height: 0px;
    background: #2e276a; }
  50% {
    width: 144px;
    height: 144px;
    background: #2e276a; }
  90% {
    width: 0px;
    height: 0px;
    background: #2e276a; }
  91% {
    width: 0px;
    height: 0px;
    background: #FFFFFF; }
  100% {
    width: 144px;
    height: 144px;
    background: #FFFFFF; } }
@keyframes in {
  0% {
    width: 144px;
    height: 144px;
    background: #FFFFFF; }
  40% {
    width: 0px;
    height: 0px;
    background: #FFFFFF; }
  41% {
    width: 0px;
    height: 0px;
    background: #2e276a; }
  50% {
    width: 144px;
    height: 144px;
    background: #2e276a; }
  90% {
    width: 0px;
    height: 0px;
    background: #2e276a; }
  91% {
    width: 0px;
    height: 0px;
    background: #FFFFFF; }
  100% {
    width: 144px;
    height: 144px;
    background: #FFFFFF; } }
@-o-keyframes out1 {
  0% {
    width: 0px;
    height: 0px; }
  30% {
    width: 120vw;
    height: 120vw; }
  100% {
    width: 120vw;
    height: 120vw; } }
@-moz-keyframes out1 {
  0% {
    width: 0px;
    height: 0px; }
  30% {
    width: 120vw;
    height: 120vw; }
  100% {
    width: 120vw;
    height: 120vw; } }
@-webkit-keyframes out1 {
  0% {
    width: 0px;
    height: 0px; }
  30% {
    width: 120vw;
    height: 120vw; }
  100% {
    width: 120vw;
    height: 120vw; } }
@keyframes out1 {
  0% {
    width: 0px;
    height: 0px; }
  30% {
    width: 120vw;
    height: 120vw; }
  100% {
    width: 120vw;
    height: 120vw; } }
@-o-keyframes out2 {
  0% {
    width: 0px;
    height: 0px; }
  30% {
    width: 0px;
    height: 0px; }
  60% {
    width: 120vw;
    height: 120vw; }
  100% {
    width: 120vw;
    height: 120vw; } }
@-moz-keyframes out2 {
  0% {
    width: 0px;
    height: 0px; }
  30% {
    width: 0px;
    height: 0px; }
  60% {
    width: 120vw;
    height: 120vw; }
  100% {
    width: 120vw;
    height: 120vw; } }
@-webkit-keyframes out2 {
  0% {
    width: 0px;
    height: 0px; }
  30% {
    width: 0px;
    height: 0px; }
  60% {
    width: 120vw;
    height: 120vw; }
  100% {
    width: 120vw;
    height: 120vw; } }
@keyframes out2 {
  0% {
    width: 0px;
    height: 0px; }
  30% {
    width: 0px;
    height: 0px; }
  60% {
    width: 120vw;
    height: 120vw; }
  100% {
    width: 120vw;
    height: 120vw; } }
.fade12 {
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none; }

/*# sourceMappingURL=style.css.map */
