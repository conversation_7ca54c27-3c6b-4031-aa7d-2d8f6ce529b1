.popup {
    height: 100vh;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background-color: rgba($siyah-renk, .8);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all .3s;

    &:target {
        opacity: 1;
        visibility: visible;
    }

    &__kapat {

        &:link,
        &:visited {
            color: $siyah-renk;
            position: absolute;
            top: 0rem;
            right: 1.5rem;
            font-size: 4rem;
            display: inline-block;
            transition: all .2s;

            @include responsive(tablet-kucuk) {
                font-size: 7rem;
            }
        }

        &:hover {
            color: $anarenk-koyu-kirmizi;
        }
        
    }

    &__content {
        @include absoluteortala;
        width: 30%;
        height: 50rem;
        background-color: $beyaz-renk;
        box-shadow: 0 2rem 4rem rgba($siyah-renk, .2);

        @include responsive(tablet-kucuk) {
            width: 80%;
            height: 80rem;
        }

        @include responsive(telefon) {
            width: 80%;
            height: 80rem
        }
    }
}