header .item {
	position: relative;
	margin-top: 87px;
  }
  
  header .item img {
	width: 100%;
	height: 100%;
	object-fit: cover;
  }
  
  header .item .cover {
	padding: 75px 0;
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background: rgba(0, 0, 0, 0);
	display: flex;
	align-items: center;
  }
  
  header .item .cover .header-content {
	position: relative;
	padding: 56px;
	overflow: hidden;
	margin-top: 8px;

	@media only screen and (max-width: 1024px) {

	}
	@include responsive(tablet-kucuk) {
		padding: 0px;
		margin-top: -7rem;
		margin-left: 0rem;

	}


  }
  
  header .item .cover .header-content .line {
	content: "";
	display: inline-block;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	position: absolute;
  }
  
  header .item .cover .header-content h2 {
    font-weight: 900;
    font-size: 50px;
    color: $beyaz-renk;
    text-align: center;
	transform: translateY(-0.3rem);
	animation: butonefekt .2s ease-out .20s;
    animation-fill-mode: backwards;
	transition: all 1s;

	@media only screen and (max-width: 1024px) {
		font-size: 46px;
	}

	@include responsive(tablet-kucuk) {
		font-size: 30px;
		font-weight: 900;
		width: 100%;
		text-align: center;
	  }


  }
  
  header .item .cover .header-content h1 {
    font-size: 20px;
    font-weight: 600;
    word-spacing: 3px;
    color: #fff;
    padding: 5px;
    text-align: center;
    margin-bottom: 1rem;
	transform: translateY(-0.3rem);
	animation: butonefekt .3s ease-out .60s;
    animation-fill-mode: backwards;
	transition: all 1s;

	@media only screen and (max-width: 1024px) {
		font-size: 25px;
	} 

	@include responsive(tablet-kucuk) {
		font-size: 17px;
		font-weight: 500;
		width: 100%;
		padding: 1px;
	  }

 
  }
  
  header .item .cover .header-content h4 {
	font-size: 25px;
	font-weight: 600;
	line-height: 40px;
	color: #fff;

	@media only screen and (max-width: 1024px) {
		font-size: 30px;
	}

	@include responsive(tablet-kucuk) {
		font-size: 17px;
	}


  }
  
  header .owl-item.active h1 {
	-webkit-animation-duration: 50s;
	animation-duration: 0.8s;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
	animation-name: fadeInDown;
	animation-delay: 0.4s;
  }
  
  header .owl-item.active h2 {
	-webkit-animation-duration: 50s;
	animation-duration: 0.8s;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
	animation-name: fadeInDown;
	animation-delay: 0.4s;
  }
  
  header .owl-item.active h4 {
	-webkit-animation-duration: 50s;
	animation-duration: 0.8s;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
	animation-name: fadeInUp;
	animation-delay: 0.4s;
  }
  
  header .owl-item.active .line {
	-webkit-animation-duration: 5s;
	animation-duration: 0.8s;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
	animation-name: fadeInLeft;
	animation-delay: 0.4s;
  }

  
  header .owl-nav .owl-prev {
	position: absolute;
	left: 15px;
	top: 30%;
	opacity: 0;
	-webkit-transition: all 0.4s ease-out;
	transition: all 0.4s ease-out;
	width: 40px;
	cursor: pointer;
	height: 40px;
	position: absolute;
	display: block;
	z-index: 1000;
	border-radius: 0;
  }
  
  header .owl-nav .owl-prev span {
	font-size: 8.6875rem;
	color: #fff;
  }
  
  header .owl-nav .owl-prev:focus {
	outline: 0;
  }
  
  header .owl-nav .owl-prev:hover {
  }
  
  header .owl-nav .owl-next {
	position: absolute;
	right: 15px;
	top: 30%;
	opacity: 0;
	-webkit-transition: all 0.4s ease-out;
	transition: all 0.4s ease-out;
	width: 40px;
	cursor: pointer;
	height: 40px;
	position: absolute;
	display: block;
	z-index: 1000;
	border-radius: 0;
  }
  
  header .owl-nav .owl-next span {
	font-size: 8.6875rem;
	color: #fff;
  }
  
  header .owl-nav .owl-next:focus {
	outline: 0;
  }
  
  header .owl-nav .owl-next:hover {
  }
  
  header:hover .owl-prev {
	left: 0px;
	opacity: 1;
  }
  
  header:hover .owl-next {
	right: 0px;
	opacity: 1;
  }

  .slider {
	  position: relative;
	  width: 100%;
	  min-height: 100vh;
	  display: flex;
	  justify-content: center;
	  align-items: flex-end;
  }
  .slider input[type="radio"] {
	  position: relative;
	  z-index: 888;
	  margin: 5px;
	  margin-bottom: 40px;
	  outline: none;
	  cursor: pointer;
  }
  