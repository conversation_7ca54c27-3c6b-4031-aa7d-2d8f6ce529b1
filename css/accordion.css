.container.asa {
    padding-left: 0px;
    margin-left: -11px;
}
.question{ font-family: inherit; font-size:18px;color:#000000;}
.answer {font-family: inherit; color: #000000;}

.question {
    font-size: 1.5rem;
    font-weight: 600;
    padding: 16px;
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer; }
    @media only screen and (max-width: 56.25em) {
      .question {
        font-size: 2.9rem; } }
    @media only screen and (max-width: 56.25em) {
      .question {
        font-size: 2.9rem; } }
  
  .question::after {
    content: "\002B";
    font-size: 2.2rem;
    position: absolute;
    right: 20px;
    transition: 0.2s;  }
    @media only screen and (max-width: 56.25em) {
      .question::after {
        font-size: 4rem; } }
    @media only screen and (max-width: 56.25em) {
      .question::after {
        font-size: 4rem; } }
  
  .question.active::after {
    transform: rotate(45deg); }
  
  .answercont {
    max-height: 0;
    overflow: hidden;
    transition: 0.3s; }
  
  .answer {
    padding: 1px;
    font-size: 16px;
    padding-left: 19px; }
