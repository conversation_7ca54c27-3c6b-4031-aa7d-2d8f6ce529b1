/*-----------------------------------------------------------

  Template Name: Insurancex Html5 & Css3 Web Site Template
  Template URI: https://www.templatemonster.com/authors/garantiwebt/
  Description: Insurancex Html5 & Css3 Web Site Template
  Author: adem <PERSON>
  Author URI: https://www.templatemonster.com/authors/garantiwebt/
  Version: 1.0.0

-------------------------------------------------------------*/

@import "kuramlar/mixin";
@import "temel/araclar";
@import "kuramlar/degiskenler";
@import "temel/temel";
@import "temel/cursor";
@import "kuramlar/fonksiyonlar";
@import "sayfalar/home-sections";

@import "temel/animasyonlar";

@import "temel/tipografi";
@import "bilesenler/swiper";
@import "layout/header";
@import "layout/tablolar";
@import "bilesenler/butonlar";
@import "bilesenler/markalar";
@import "bilesenler/galeri";
@import "bilesenler/hizmetler";
@import "bilesenler/yorumlar";
@import "bilesenler/paketler";
@import "bilesenler/popup";
@import "bilesenler/team";
@import "bilesenler/search";
@import "layout/footer";
@import "layout/menu";
@import "bilesenler/projeler";
@import "bilesenler/form";
@import "bilesenler/social";
@import "bilesenler/slider";
@import "bilesenler/yukari-cik";


@mixin transition-cubic{
	transition-duration: 1s;	-webkit-transition-duration: 1s;	
	transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
	-webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1)}

@mixin transition{-webkit-transition: all .35s ease-in-out;
	-moz-transition: all .35s ease-in-out; 
	-ms-transition: all .35s ease-in-out; 
	-o-transition: all .35s ease-in-out; 
	transition: all .35s ease-in-out;}


@mixin font-one{font-family: 'Barlow', sans-serif;}
@mixin font-two{font-family: "Mohave", sans-serif;}

$anarenk-koyu-kirmizi : #2e276a;



*{outline: none !important;}
body{margin: 0; padding: 0; font-family: "Mohave"; font-size: 18px; color: $anarenk-koyu-kirmizi;  text-rendering: optimizeLegibility; -moz-osx-font-smoothing: grayscale; -webkit-font-smoothing: antialiased; }



/* LINKS */
a{color: $anarenk-koyu-kirmizi; @include transition;}
a:hover{text-decoration: underline; color: $anarenk-koyu-kirmizi;}


/* HTML TAGS */
img{max-width: 100%;}
p{@include font-one ;}




/* FORM ELEMENTS */
input[type="text"]{width: 420px; max-width: 100%; height: 70px; padding: 0 30px; border: 1px solid #cecece;}
input[type="email"]{width: 420px; max-width: 100%; height: 70px; padding: 0 30px; border: 1px solid #cecece;}
input[type="search"]{width: 420px; max-width: 100%; height: 70px; padding: 0 30px; border: 1px solid #cecece;}
input[type="password"]{width: 420px; max-width: 100%; height: 70px; padding: 0 30px; border: 1px solid #cecece;}

input[type="radio"]{ width: 18px; height: 18px; display: inline-block; margin-right: 4px; transform: translateY(3px); appearance:none; background: #ededed; border-radius: 50%;}
input[type="radio"]:checked{ border: 6px solid $anarenk-koyu-kirmizi;}

input[type="checkbox"]{ width: 18px; height: 18px; display: inline-block; margin-right: 4px; transform: translateY(3px); appearance:none; background: #ededed; }
input[type="checkbox"]:checked{ border: 6px solid $anarenk-koyu-kirmizi;}



textarea{width: 520px; max-width: 100%; height: 140px; padding: 30px; border: 1px solid #cecece;}

select{width: 420px; max-width: 100%; height: 70px; padding: 0 30px; border: 1px solid #cecece;}

select { -webkit-appearance: none; -moz-appearance: none; background-image: linear-gradient(45deg, transparent 50%, gray 50%), linear-gradient(135deg, gray 50%, transparent 50%), linear-gradient(to right, #ccc, #ccc);
  background-position: calc(100% - 30px) 34px, calc(100% - 25px) 34px, calc(100% - 3.5em) 20px; background-size: 5px 5px, 5px 5px, 1px 40px; background-repeat: no-repeat;}

select:focus { background-image:
    linear-gradient(45deg, gray 50%, transparent 50%),
    linear-gradient(135deg, transparent 50%, gray 50%),
    linear-gradient(to right, #ccc, #ccc);
  background-position: calc(100% - 25px) 34px, calc(100% - 30px) 34px, calc(100% - 3.5em) 20px; background-size: 5px 5px, 5px 5px, 1px 40px; background-repeat: no-repeat;
  border-color: gray;
  outline: 0;
}


select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #000;
}

input[type="submit"]{height: 70px; display: inline-block; font-size: 14px; font-weight: 500; color: #fff; background: $anarenk-koyu-kirmizi; border:none; padding: 0 50px;}

button[type="submit"]{height: 70px; display: inline-block; font-size: 14px; font-weight: 500; color: #fff; background: $anarenk-koyu-kirmizi; border:none; padding: 0 50px;
	i{display: inline-block; margin-right: 8px; font-size: 18px; transform: translateY(2px);}}




/* CUSTOM CONTAINER */
@media (min-width: 1170px) { .container { max-width: 1100px; } }
@media (min-width: 1280px) { .container { max-width: 1260px; } }





/* CUSTOM CLASSES */
.overflow{overflow: hidden;}
.no-gutters{padding: 0; margin: 0;}





/* SPACING */
.no-spacing{margin: 0 !important; padding: 0 !important;}
.no-top-spacing{margin-top: 0 !important; padding-top: 0 !important;}
.no-bottom-spacing{margin-bottom: 0 !important; padding-bottom: 0 !important;}





/* HAMBURGER MENU */
.hamburger-menu { width: 30px; height: 20px;  position: relative; -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg);  transform: rotate(0deg);  transition-duration: 500ms;	-webkit-transition-duration: 500ms;	transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);	-webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);  cursor: pointer; }
.hamburger-menu span {display: block;position: absolute;height: 2px;width: 100%;background: #fff;opacity: 1;left: 0;-webkit-transform: rotate(0deg);-moz-transform: rotate(0deg);-o-transform: rotate(0deg);transform: rotate(0deg); -webkit-transition: .25s ease-in-out; -moz-transition: .25s ease-in-out; -o-transition: .25s ease-in-out; transition: .25s ease-in-out;}
.hamburger-menu span:nth-child(1) {top: 0px; width: 100%;}
.hamburger-menu span:nth-child(2) {  top: 9px; width: 22px;}
.hamburger-menu span:nth-child(3) {  top: 18px;	width: 100%;}
.hamburger-menu:hover span{ width: 100% !important;}
.hamburger-menu.open span{ width: 20px !important;}
.hamburger-menu.open span:nth-child(1) {  top: 9px; right: 0;  -webkit-transform: rotate(135deg);  -moz-transform: rotate(135deg);  -o-transform: rotate(135deg);  transform: rotate(135deg); width: 28px !important}
.hamburger-menu.open span:nth-child(2) {  opacity: 0;  left: -20px;}
.hamburger-menu.open span:nth-child(3) {  top: 9px; right: 0;  -webkit-transform: rotate(-135deg);  -moz-transform: rotate(-135deg);  -o-transform: rotate(-135deg);  transform: rotate(-135deg); width: 28px !important}




/* ODOMETER */
.odometer.odometer-auto-theme{padding: 0;}
.odometer.odometer-auto-theme .odometer-digit, .odometer.odometer-theme-car .odometer-digit{padding: 0;}
.odometer.odometer-auto-theme .odometer-digit .odometer-digit-inner, .odometer.odometer-theme-car .odometer-digit .odometer-digit-inner{left:-4px;}



/* SWIPER PAGINATION */
.swiper-pagination{width: 100%;
	.swiper-pagination-bullet{width: 3vw; height: 4px;  background: #fff; @include transition; opacity: 0.8; border-radius: 5px;
		&.swiper-pagination-bullet-active{width: 6vw; background: #c1c1c1; opacity: 1;}}}


/* CUSTOM BUTTON */
.col-12.text-center .custom-button{ margin-top: 50px;}
.custom-button{ height: 70px; line-height: 70px; display: inline-block; background: $anarenk-koyu-kirmizi; color: #fff; padding: 0 50px; position: relative;font-size: 16px;border-radius: 55px;
	&:before{content: ""; width: 0; height: 100%; position: absolute; top: 0; left: 0; opacity: 0.05; @include transition-cubic;}
	&:hover{text-decoration: none; color: #fff;}
	&:hover:before{width: 100%;}}

.col-12.text-center .custom-buttonw{ margin-top: 50px;}
.custom-buttonw{ height: 70px; line-height: 70px; display: inline-block; background: $beyaz-renk; color: $anarenk-koyu-kirmizi; padding: 0 50px; position: relative;font-size: 16px;border-radius: 55px;
	&:before{content: ""; width: 0; height: 100%; background: #000; position: absolute; top: 0; left: 0; opacity: 0.05; @include transition-cubic;}
	&:hover{text-decoration: none; color: $anarenk-koyu-kirmizi;}
	&:hover:before{width: 100%;}}
	



/* REVEAL EFFECT */
.wow.fade{ opacity: 0; transition: opacity 0.5s ease; transition-delay: 0.2s;}
.wow.fade.animated{opacity: 1;} 
.reveal-effect { float: left; position: relative;
	&.animated:before{content: ""; width:100%; height: 100%; background: #eee; position: absolute; left: 0; top: 0; animation: 1s reveal linear forwards; -webkit-animation-duration: 1s; z-index: 1;
	-moz-animation-duration: 1s; 
	-ms-animation-duration: 1s; 
	-o-animation-duration: 1s; 
	animation-duration: 1s; 
	-webkit-animation-fill-mode: forwards; 
	-moz-animation-fill-mode: forwards; 
	-ms-animation-fill-mode: forwards; 
	-o-animation-fill-mode: forwards; 
	animation-fill-mode: forwards; 
	-webkit-animation-timing-function: cubic-bezier(.785,.135,.15,.86); 
	-moz-animation-timing-function: cubic-bezier(.785,.135,.15,.86); 
	-o-animation-timing-function: cubic-bezier(.785,.135,.15,.86); 
	-ms-animation-timing-function: cubic-bezier(.785,.135,.15,.86); 
	animation-timing-function: cubic-bezier(.785,.135,.15,.86);}}
	
.reveal-effect.animated> *{ animation: 1s reveal-inner linear forwards;}
@-webkit-keyframes reveal {
    0% {
        left: 0;
		width: 0;
        
    }

    50% { 
        left: 0;
		width: 100%;
    }
	51% { 
        left: auto;
		right: 0;
		
    }
    100% { 
        left: auto;
        right: 0;
		width: 0;
    }

    
}

@-webkit-keyframes reveal-inner {
    0% {
        visibility: hidden;
		opacity: 0;
        
    }
	50%{ 
        visibility: hidden;
		opacity: 0;
    }
	51% { 
        visibility: visible;
		opacity: 1;
    }
    
	
    100% { 
        visibility: visible;
		opacity: 1;
    }

}





/* PRELOADER */
.preloader{width: 100%; height: 100%; display: flex; flex-wrap: wrap; align-items: center; justify-content: center; position: fixed; z-index: 99; right: 0; top:0; background: $anarenk-koyu-kirmizi; @include transition-cubic;
	figure{ width: 140px; height: 140px; display: flex; align-items: center; justify-content: center; border: 1px solid rgba(255,255,255,0.2); border-radius: 50%; animation: fadeup 0.30s; position: relative;
		&:after{content: ""; width: 100%; height: 100%; border: 1px solid transparent; border-top: 1px solid #fff; border-radius: 50%; position: absolute; left: 0; top: 0; animation: rotate1 0.60s infinite;}}
	img{height: 50px; display: inline-block; ;}}
.page-loaded .preloader{ top: -100%;}
@keyframes fadeup {
	0%{transform:translateY(20px); opacity: 0;}
    100%{transform:translateY(0); opacity: 1;}
}
@keyframes rotate1 {
	0%{transform:rotate(0deg); }
    100%{transform:rotate(360deg); }
}





/* PAGE TRANSITION */
.page-transition{width: 100%; height: 0; position: fixed; z-index: 99; left: 0; bottom:0; background: $anarenk-koyu-kirmizi; @include transition-cubic;
	&.active{height: 100%;}}




/* SIDE WIDGET */
.side-widget{width: 400px; height: 100vh; max-width: 100%; display: flex; flex-wrap: wrap; align-items: center; position: fixed; left: -100%; top: 0; @include transition-cubic; background: #2e276a; z-index: 6; box-shadow: 0 0 60px rgba(0,0,0,0.4); padding:20px 30px; color: #fff;
	.inner{display: flex; flex-wrap: wrap; align-items: center; overflow-y: auto;  height: 100%;}
	.logo{width: 100%; display: block; margin-bottom: 40px; 
		img{height: 49px;}}
	.show-mobile{display: none;}
	.hide-mobile{ width: 100%; display: inline-block; }
	.gallery{ display: flex; flex-wrap: wrap;  margin-bottom: 30px;
		a{width: 50%; padding-right: 3px;
			&:last-child{padding-left: 3px;}}}
	p{width: 100%; display: block; color: #fff;}
	.widget-title{width: 100%; display: block; margin-bottom: 10px; font-size: 18px; color: $anarenk-koyu-kirmizi;}
	.address{width: 100%; display: block; margin-bottom: 20px;
		a{display: inline-block; color: #fff; text-decoration: underline;
			&:hover{text-decoration: none;}}}
	.social-media{width: 100%; display: block; margin: 0; padding: 0;
		li{display: inline-block; margin-right: 20px; padding: 0; list-style: none;
			a{color: #fff; font-size: 13px; font-weight: 600;
				&:hover{color: $anarenk-koyu-kirmizi;}}}}
	.custom-menu{width: 100%; display: block; margin-bottom: 20px; margin-top: 20px;
		ul{width: 100%; display: block; margin: 0; padding: 0;
			li{display: inline-block; margin:3px 0; margin-right: 10px; padding: 0; list-style: none;
				ul{display: none; padding-left: 20px; margin-bottom: 10px;}
				a{color: #fff; font-size: 18px; font-weight: 600;
					&:hover{text-decoration: none; color: $anarenk-koyu-kirmizi;}}}}}
	.site-menu{width: 100%; display: block; margin-bottom: 20px; margin-top: 20px;
		ul{width: 100%; display: block; margin: 0; padding: 0;
			li{display: block; margin:3px 0; padding: 0; list-style: none;
				ul{display: none; padding-left: 20px; margin-bottom: 10px;}
				a{color: #fff; font-size: 18px; font-weight: 600;
					&:hover{text-decoration: none; color: $anarenk-koyu-kirmizi;}}}}}
	small{font-size: 13px; width: 100%; display: block; margin-top: 20px; @include font-one;}
	&.active{left: 0; z-index: 8888;}}






/* TOPBAR */
.topbar{width: 100%; display: flex; flex-wrap: wrap; background: $anarenk-koyu-kirmizi; padding: 10px 0; color: #fff; display: none;
	div{display: inline-block; font-size: 16px; @include font-one;
		b{font-weight: 500; display: inline-block; margin-right: 6px; opacity: 0.5;}
		a{display: inline-block; color: #fff;}}}




/* NAVBAR */
.navbar{width: 100%; display: flex; flex-wrap: wrap; padding:0; position: absolute; left: 0; top: 0; z-index: 5;position: fixed;
    background: #2e276a;
	.logo{margin-right: auto; padding: 30px 0; padding-right: 30px;
		a{display: inline-block;
			img{height: 75px;}}}
	.site-menu{margin: 0 auto;
		ul{display: flex; flex-wrap: wrap; margin: 0; padding: 0;
			li{display: inline-block; margin: 0; padding: 0 15px; list-style: none; @include transition;
				a{color: #fff; font-weight: 500;font-size: 17px;
					&:hover{text-decoration: none;}}}}}
	.hamburger-menu{margin-left: auto;}
	.navbar-button{margin-left: 30px;
		a{ height: 70px; line-height: 70px; display: inline-block; background: $beyaz-renk; color: $anarenk-koyu-kirmizi; padding: 0 21px; position: relative;font-size: 17px;border-radius: 55px;
      font-weight: 600;
		&:before{content: ""; width: 0; height: 100%; background: #000; position: absolute; top: 0; left: 0; opacity: 0.05; @include transition-cubic;}
	&:hover{text-decoration: none; color: #2e276a;}
	&:hover:before{width: 100%;}}}}


/* SLIDER */
.slider{width: 100%; height: 100vh; display: flex; flex-wrap: wrap; align-content: center; position: relative;
	

.main-slider{width: 100%; height: 100vh; display: flex; flex-wrap: wrap; position: relative; overflow: hidden;
		.swiper-slide{display: flex; flex-wrap: wrap; align-items: center;  padding: 0 15%; padding-top: 150px; background: #000;
			.slide-image{width: 100%; height: 100%; position: absolute; left: 0; top: 0; background-size: cover !important; background-position: center !important; opacity: 0.7;
				&:after{content: ""; width: 100%; height: 100%; position: absolute; left: 0; top: 0;
background: rgb(0,0,0);
background: -moz-linear-gradient(351deg, rgba(0,0,0,0.0018382352941176405) 0%, rgba(0,0,0,1) 100%);
background: -webkit-linear-gradient(351deg, rgba(0,0,0,0.0018382352941176405) 0%, rgba(0,0,0,1) 100%);
background: linear-gradient(351deg, rgba(0,0,0,0.0018382352941176405) 0%, rgba(0,0,0,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#000000",endColorstr="#000000",GradientType=1);
				 opacity: 0.4;}}
			.container{ color: #fff; position: relative; z-index: 1;
				h1{ width: 100%; display: block; font-size: 60px;  margin-bottom: 10px;font-weight: 800;}
				p{width: 100%; display: block; color: #fff; margin-bottom: 50px; font-size: 20px;}
				a{height: 70px; line-height: 70px; display: inline-block; padding: 0 50px; background: $anarenk-koyu-kirmizi; color: #fff;  @include transition; position: relative;font-size: 16px;
          font-weight: 600;
				&:before{content: ""; width: 0; height: 100%; background: #000; position: absolute; top: 0; left: 0; opacity: 0.05; @include transition-cubic;}
	&:hover{text-decoration: none; color: #fff;}
	&:hover:before{width: 100%;}}}}}
	.button-prev{ width: 60px; height: 60px; line-height: 60px; text-align: center; position: absolute; left: 50px; top: 50%; border: 1px solid rgba(255,255,255,0.3);; color: #fff; z-index: 3; font-size: 23px; cursor: pointer; @include transition;
		&:hover{background: $anarenk-koyu-kirmizi; border-color: transparent;}}
	.button-next{ width: 60px; height: 60px; line-height: 60px; text-align: center; position: absolute; right: 50px; top: 50%; border: 1px solid rgba(255,255,255,0.3);; color: #fff; z-index: 3; font-size: 23px; cursor: pointer;  @include transition;
		&:hover{background: $anarenk-koyu-kirmizi; border-color: transparent;}}
	
	
	

}




/* PAGE HEADER */
.page-header{width: 100%; height: 926px; max-height:105vh; display: flex; flex-wrap: wrap; align-items: center; position: relative; background-size: cover !important; padding-top: 1px;
	&:after{content: ""; width: 100%; height: 100%; position: absolute; left: 0; top: 0;}
	.container{position: relative; z-index: 1; color: #fff;
	h2{ width: 100%; display: block; font-size: 50px;  margin-bottom: 0;line-height: 6rem;}
	p{width: 100%; display: block;  margin-bottom: 50px; font-size: 18px;}}}





/* CONTENT SECTION */
.content-section{width: 100%; display: flex; flex-wrap: wrap; padding: 100px 0; position: relative; background: #2e276a;
	&.left-white-spacing{ position: relative;
		.container{position: relative; z-index: 1;}
		&:before{content: ""; width: 30%; height: 100%; background: #fff; position: absolute; left: 0; top: 0;}}
	&.bottom-dark-spacing{ position: relative; padding-bottom: 0 !important;
		.container{position: relative; z-index: 1;}
		&:before{content: ""; width: 30%; height: 100%; background: #fff; position: absolute; left: 0; top: 0;}
		&:after{content: ""; width: 100%; height: 140px; background: $anarenk-koyu-kirmizi; position: absolute; left: 0; bottom: 0;}}}



/* SECTION TITLE */
.section-title{width: 100%; display: flex; flex-wrap: wrap; margin-bottom: 50px; text-align: center;
	figure{width: 100%; display: block; margin-bottom: 15px;
		img{height: 40px;}}
	h6{width: 100%; display: block;}
	h2{ width: 100%; display: block; margin-bottom: 0; font-size: 70px; color: $anarenk-koyu-kirmizi;}
	p{ width: 100%; display: block; margin-bottom: 0; opacity: 0.7;}}



/* IMAGE BOX */
.col-lg-4:nth-child(1) .image-box{ padding-right: 30px;}
.col-lg-4:nth-child(2) .image-box{ margin-top: 60px;}
.col-lg-4:nth-child(3) .image-box{ padding-left: 30px;}
.image-box{width: 100%; display: flex; flex-wrap: wrap;
	figure{width: 100%; display: block; margin-bottom: 20px;
		img{width: 100%;}}
	.time{display: inline-block; color: $anarenk-koyu-kirmizi;  margin-right: 6px; margin-top: 4px;}
	h6{display: block; font-size: 32px;}}






/* SIDE CONTENT */
.side-content{width: 100%; display: flex; flex-wrap: wrap;
	&.left{padding-right: 20%;}
	&.right{padding-left:10%; }
	&.light{color: #fff;
		h2{color: #fff;}}
	h2{width: 100%; display: block; font-size: 50px; font-weight: 800; color: $beyaz-renk;}
	h6{width: 100%; display: block; font-weight: 800; font-size: 19px;color: #ffffff;text-align: center;}
	.custom-button{margin-top: 30px;}
	figure{width: 100%; display: block;
		img{height: 100px;}}
	form{width: 100%; display: flex; margin-top: 40px;
		input[type="text"]{border: none; }
		button[type="submit"]{width: 70px; padding: 0; text-align: center; margin-left: -70px; background: none; color: $anarenk-koyu-kirmizi;}}}



/* SIDE IMAGE */
.side-image{width: 100%; display: flex; flex-wrap: wrap; margin: 0; position: relative;
	&.full-left{width: 47vw; float: right;}
	&.full-right{width: 49.6vw; float: left;}
	img{width: 100%;}
	.side-timetable{ width: 340px; display: flex; flex-wrap: wrap; position: absolute; left: -50px; bottom: 50px; background: $anarenk-koyu-kirmizi; padding: 40px; margin: 0; z-index: 1;
		li{ width: 100%; display: flex; flex-wrap: wrap; margin: 10px 0; padding: 0; list-style: none;
			span{ color: #fff;}
			b{font-weight: 400; margin-left: auto; color:$anarenk-koyu-kirmizi;}}}}





/* SIDE GALLERY */
.side-gallery{width:calc(50vw - 15px); display: flex; flex-wrap: wrap; margin: 0;
	figure{width: calc(33.33333% - 10px); display: inline-block; margin: 5px 0; margin-left: 10px; background: $anarenk-koyu-kirmizi; position: relative;
		&:before{content: ""; width: 4px; height: 50px; background: #fff; position: absolute; left: calc(50% - 2px); top: calc(50% - 25px); z-index: 1; @include transition; opacity: 0}
		&:after{content: ""; width: 50px; height: 4px; background: #fff; position: absolute; left: calc(50% - 25px); top: calc(50% - 2px); z-index: 1; @include transition; opacity: 0}
		img{width: 100%; @include transition;}
		&:hover img{ opacity: 0.3;}
		&:hover:before{opacity: 1;}
		&:hover:after{opacity: 1;}}}





/* SIDE MEMBER */
.side-member{ width: 100%; display: flex; flex-wrap: wrap; align-items: center; position: relative; overflow: hidden; margin-bottom: 0;
	img{width: 100%; display: block;}
	figcaption{width: 100%; height: 140px; display: flex; flex-wrap: wrap; align-items: center; background: $anarenk-koyu-kirmizi; color: #fff; text-align: center;
		h5{width: 100%; display: block; font-size: 50px; line-height: 1; font-weight: 500; margin-top: auto; margin-bottom: 0;}
		span{width: 100%; display: block; font-size: 20px; margin-bottom: auto;}}}




/* PROGRESS BAR */
.custom-progress{width: 80%; display: flex; flex-wrap: wrap; margin-top: 10px; margin-bottom: 30px;
	&:last-child{margin-bottom: 0;}
	h6{display: inline-block; font-size: 20px; font-weight: 500; margin: 0;}
	span{margin-left: auto; font-size: 20px;  color: $anarenk-koyu-kirmizi;}
	.progress-bar{width: 100%; height: 5px; background: #eee; display: inline-block; margin-top: 10px; border-radius: 0; position: relative;
		.progress{width: 0; height: 5px; background: $anarenk-koyu-kirmizi; position: absolute; left: 0; top: 0; @include transition-cubic;}
		&.animated .one{ width: 80%;}
		&.animated .two{ width: 67%;}
	&.animated .three{ width: 92%;}
	&.animated .four{ width: 88%;}}}




/* TAB WRAPPER */
.tab-wrapper{width: 100%; display: flex; flex-wrap: wrap;
	.tab-nav{ width: 25%; margin: 0; padding: 0; padding-right: 40px; position: relative; z-index: 1;
		li{ width: 100%; display: block; margin-bottom: 10px; list-style: none;
			&.active a{width: calc(100% + 60px); margin-right: -60px; background: $anarenk-koyu-kirmizi; color: #fff;
				&:hover{background: $anarenk-koyu-kirmizi;}}
			a{ width: 100%; display: block; background: #f4f4f4; padding: 25px; font-weight: 700;
				&:hover{background: #f0f0f0; text-decoration: none;}}}}
	.tab-item{width: 75%; display: none;
		&.active-item{display: flex;}
		.tab-inner{width: 100%; display: flex; position: relative; background: $anarenk-koyu-kirmizi;
			ul{width: calc(350px - 100px); height: 40vw; overflow: auto; float: left; color: #fff; margin: 40px;  margin-left: 60px; padding: 0;
				li{width: 100%; display: block; margin-bottom: 20px; padding: 0; list-style: none;
					span{width: 100%; display: block; color: $anarenk-koyu-kirmizi;}
					h6{width: 100%; display: block;}
					small{width: 100%; display: block; @include font-one; opacity: 0.7;}}}
			figure{width: 100%; float: left; margin: 0;
				img{width: 100%;}}}}}



/* COUNTER BOX */
.counter-box{width: 100%; display: flex; flex-wrap: wrap; padding: 40px; text-align: center; background: #fff; box-shadow: 0 0 40px rgba(0,0,0,0.05);
	figure{width: 100%; display: block; margin-bottom: 15px;
		img{height: 109px;}}
	.odometer{    line-height: 1;
		margin: 0 auto;
		font-size: 100px;
		color: $siyah-renk;
		font-weight: 700;}
	h6{width: 100%; height: 26px; line-height: 26px; display: block; font-size: 22px;  margin-bottom: 0; margin-top: 20px; color: $siyah-renk; position: relative;font-weight: 700;
		&:after{content: ""; width: 100px; height: 6px; background: $anarenk-koyu-kirmizi; position: absolute; left: calc(50% - 50px); bottom: -40px;}}}





/* SERVICE BOX */
.service-box{width: 100%; display: flex; flex-wrap: wrap; align-items: center; position: relative; margin: 15px 0; background: $anarenk-koyu-kirmizi;
	&:before{content: ""; width: 100%; height: 100%; background: $anarenk-koyu-kirmizi; position: absolute; left: 0; top: 0; z-index: 0; @include transition}
	&:hover img{opacity: 0; transform: scale(1.1);}
	&:hover figcaption p{margin-bottom: 30px; margin-top: 10px; opacity: 0.7;}
	&:hover figcaption a{margin-bottom: 0; opacity: 1;}
	&:hover:before{ transform: scale(1.1);}
	*{@include transition}
	img{width: 100%; position: relative;}
	figcaption{width: 100%;  position: absolute; left: 0; top: 50%; z-index: 1; transform: translateY(-50%); color: #fff; text-align: center;
		h6{width: 100%; display: block; font-size: 36px; font-weight: 700; margin: 0; line-height: 1;}
		p{ width: 100%; display: block; padding: 0 10%; margin-bottom: -100px; opacity: 0;}
		a{height: 70px; line-height: 68px; display: inline-block; border: 2px solid #fff; color: #fff; margin-bottom: -100px; opacity: 0;  padding: 0 50px;
		&:hover{text-decoration: none; background: #fff; color: $anarenk-koyu-kirmizi;}}
	}}




/* IMAGE OVERLAP BOX */
.image-overlap-box{width: 100%; display: flex; flex-wrap: wrap; position: relative; overflow: hidden;
	*{@include transition-cubic;}
	&:hover figure img{opacity: 0.3; transform: scale(1.05);}
	&:hover .content img{margin-top: 0; margin-bottom: -100px; opacity: 0.2; transform: scale(1.4);}
	&:hover .content p{margin-bottom: 40px; opacity: 1;}
	&:hover .content a{margin-bottom: 0; opacity: 1;}
	figure{width: 100%; display: block; margin: 0;  background: $anarenk-koyu-kirmizi;
		img{width: 100%;}}
	.content{ width: 100%; position: absolute; left: 0; top: 50%; text-align: center; transform: translateY(-50%);
		img{height: 80px; display: inline-block; margin-bottom: 20px; margin-top: 100px;}
		h6{width: 100%; display: block; font-size: 30px; font-weight: 500; color: #fff;}
		p{width: 100%; display: block; padding: 0 10%; color: #fff; margin-bottom: 0; opacity: 0;}
	a{height: 70px; line-height: 68px; display: inline-block; border: 2px solid #fff; color: #fff; margin-bottom: -100px; opacity: 0; padding: 0 50px;
		&:hover{text-decoration: none; background: #fff; color: $anarenk-koyu-kirmizi;}}}}
	




/* CUSTOM LIST */
.custom-list{width: 100%; display: flex; flex-wrap: wrap; padding: 0; margin-bottom: 20px;
	li{width: 100%; display: block; margin-bottom: 15px; padding: 0; list-style: none; @include font-one; font-size: 20px;
		&:last-child{margin-bottom: 0;}
		&:before{font-size: 16px; display: inline-block; margin-right: 12px;}}}






/* VIDEO */
.video{width: 100%; display: flex; flex-wrap: wrap; position: relative; overflow: hidden; background: $anarenk-koyu-kirmizi;
	img{width: 100%; @include transition;}
	&:hover img{opacity: 0.8;}
	a{width: 120px; height: 120px; display: flex; align-items: center; justify-content: center; background: #fff; border-radius: 50%; position: absolute; left: calc(50% - 60px); top: calc(50% - 60px); color: $anarenk-koyu-kirmizi; font-size: 30px; 
		&:hover{text-decoration: none; transform: scale(1.1);}}}



/* CAROUSEL CLASSES */
.carousel-classes{width: 100%; display: flex; flex-wrap: wrap; position: relative; overflow: hidden; padding-bottom: 50px;
	.swiper-pagination{bottom: 0;}}





/* ALL CLASSES */
.all-classes {width: calc(100% + 30px); display: flex; flex-wrap: wrap; margin: 0 -15px; margin-bottom: 100px; padding: 0; 
	li{width: 33.33333%; margin: 0; margin-top: 50px; padding: 0 15px; list-style: none;
		&:nth-child(1), &:nth-child(2), &:nth-child(3){margin-top: 0;}
		&:nth-child(3n+2){transform: translateY(100px);}}}





/* CLASS BOX */
.class-box{width: 100%; display: flex; flex-wrap: wrap; text-align: center;
	&:hover figure img{ opacity: 0.3; transform: scale(1.05);}
	figure{width: 100%; display: block; margin-bottom: 20px; background: $anarenk-koyu-kirmizi; overflow: hidden;
		img{width: 100%; @include transition;}}
	h6{width: 100%; display: block; font-size: 42px; font-weight: 700; padding: 0 15%;}
	small{width: 100%; display: block; font-size: 16px; opacity: 0.7;}}




/* IMAGE */
.image{width: 100%; display: block; margin-bottom: 30px;
	&.spacing{margin: 40px 0;}
	img{width: 100%;}}





/* TEXT BOX */
.text-box{width: 100%; display:flex; flex-wrap: wrap;
	h3{width: 100%; display: block; font-size: 36px; font-weight: 700;}
	h5{ width: 100%; display: block; color: $anarenk-koyu-kirmizi; font-weight: 500; margin-bottom: 0; font-size: 22px;} 
	p{width: 100%; display: block; margin-bottom: 20px;}}


/* PASS BOX */
.col-lg-6:nth-child(1) .pass-box{ border-right: 1px solid rgba(255,255,255,0.1);}
.pass-box{width: 100%; display: flex; flex-wrap: wrap; position: relative; text-align: center; color: #fff;
	figure{width: 100%; display: block; margin-bottom: 30px;
		img{height: 80px;}}
	h6{width: 100%; display: block; font-size: 50px; font-weight: 800;}
	p{width: 100%; display: block; padding: 0 20%; margin-bottom: 0;}}




/* RECENT NEWS */
.recent-news{width: 100%; display: flex; flex-wrap: wrap;
	&:hover figure img{ opacity: 0.3; transform: scale(1.05);}
	figure{width: 100%; display: block; margin: 0; background: $anarenk-koyu-kirmizi; position: relative; overflow: hidden;
		img{width: 100%; @include transition;}}
	.content{width: 100%; display: flex; flex-wrap: wrap; padding: 40px; border: 1px solid rgba(0,0,0,0.1); border-top:
	none;
		h3{width: 100%; display: block; margin-bottom: 15px; font-weight: 500; font-size: 34px;
			a{display: inline-block; color: $anarenk-koyu-kirmizi;
				&:hover{color: $anarenk-koyu-kirmizi; text-decoration: none;}}}
		p{width: 100%; display: block; margin-bottom:25px; opacity: 0.7;}
		small{width: 100%;  display: block;  font-size: 16px;
			span{ width: 5px; height: 5px; display: inline-block; border-radius: 50%; background: $anarenk-koyu-kirmizi; margin: 0 15px; transform: translateY(-3px);}}}}





/* BLOG BOX */
.blog-box{width: 100%; display: block; position: relative;  margin-bottom: 100px;
	&:last-child{margin-bottom: 0;}
	&:hover .content h3 a{ background-size: 100% 100%;}
	figure{width: 100%; display: block; margin-bottom: 30px; position: relative; overflow: hidden; background: $anarenk-koyu-kirmizi;
		img{ width: 100%; max-width: inherit;  }}
	.content{ width: 100%; height: 100%; display: block; background: #fff; 
		small{display: block; font-size: 15px; opacity: 0.6; margin-bottom: 10px; text-transform: uppercase;}
		h3{width: 100%; display: block; margin-bottom: 20px; font-size: 56px; line-height: 1.1; font-weight: 800;
			a{display: block; color: $anarenk-koyu-kirmizi; @include transition;
				&:hover{color: $anarenk-koyu-kirmizi; text-decoration: none;}}}
		.author{width: 100%; display: block; margin-bottom: 0; font-weight: 500; font-size: 20px;
			img{height: 70px; display: inline-block; border-radius: 50%; margin-right: 15px;}
			b{ font-weight: 500; opacity: 0.6;}}
		h6{font-size: 24px; line-height: 1.7; margin: 30px 0;}
		strong{font-weight: 600;}
		figure{margin: 30px 0;}
		blockquote{width: 100%; display: block; color: $anarenk-koyu-kirmizi; font-size: 26px; @include font-one; margin-bottom: 30px;
			&:before{content: "“"; font-size: 100px; height: 50px; line-height: 0.9; display: block;}}
		ul{padding-left: 20px;
			li{margin: 4px 0;}}
		.half-image{width: 50%; float: right; margin-left: 20px;}
		.full-width{width: calc(100% + 100px); float: left; margin-left: -50px; margin-right: -50px;}}}



/* SIDEBAR */
.sidebar{width: 100%; display: block; padding-left: 30px;
	.widget{width: 100%; display: flex; flex-wrap: wrap; border: 1px solid #eee; padding: 40px; margin-bottom: 40px; position: relative;
		*{position: relative;}
		.widget-title{ width: 100%; display: block; position: relative; z-index: 1; font-weight: 800; letter-spacing: 1px; font-size: 22px; color: $anarenk-koyu-kirmizi; margin-bottom: 20px; padding-bottom:20px; 
		&:before{content: ""; width: 100%; height: 2px; background: $anarenk-koyu-kirmizi; position: absolute; left: 0; bottom: 0; z-index: -1; }}
		form{width:100%; display: block; margin-top: 10px;
			input[type="submit"]{margin-top: 10px; background: $anarenk-koyu-kirmizi; color: #fff;}}
		.categories{width: 100%; display: block; margin: 0; padding: 0; 
			li{width: 100%; display: block; margin: 4px 0; padding: 0; list-style: none;
				a{color: $anarenk-koyu-kirmizi; font-size: 19px;}}}
		.side-gallery{width: calc(100% + 4px); float: left; margin: 0 -2px; padding: 0;
			li{width: 50%; float: left; margin: 0; padding: 2px; list-style: none;}}}}






/* BRANCH BOX */
.branch-box{width: 100%; display: flex; flex-wrap: wrap;
	h6{width: 100%; display: block; font-weight: 800; font-size: 24px; color: $anarenk-koyu-kirmizi;}
	address{width: 100%; display: block; margin-bottom: 10px;
		b{width: 100%; display: block; margin-top: 5px; font-weight: 500;}}
	a{display: inline-block; text-decoration: underline;
		&:hover{text-decoration: none; color: $anarenk-koyu-kirmizi;}}}



/* MEMBER BOX */
.member-box{width: 100%; display: flex; flex-wrap: wrap; align-items: center; position: relative; overflow: hidden; margin: 0;
	&:hover figcaption{bottom: 0; transform: translateY(0);}
	img{width: 100%; display: block;}
	figcaption{width: 100%; display: flex; flex-wrap: wrap; position: absolute; left: 0; bottom: 120px; color: #fff; transform: translateY(100%); background: $anarenk-koyu-kirmizi; text-align: center; padding: 30px; @include transition;
		h6{width: 100%; display: block; font-size: 34px; font-weight: 700;}
		small{width: 100%; display: block; margin-bottom: 15px;}
		p{width: 100%; display: block; padding: 0 10%; opacity: 0.7;}
		ul{ width: 100%; display: flex; justify-content: center; margin: 0; padding: 0;
			li{display: inline-block; margin: 0 7px; padding: 0; list-style: none;
				a{color: #fff; float: left; font-size: 13px;}}}}}




/* CTA BOX */
.cta-box{width: 100%; max-width: 600px; display: inline-block; margin: 0 auto; background: rgba(0,0,0,0.7); color: #fff; padding: 50px; text-align: center;
	h2{width: 100%; display: block; font-size: 50px; font-weight: 500;}
	p{width: 100%;}
	.custom-button{margin-top: 10px !important;}}





/* TESTIMONIALS */
.testimonial{width: 100%; display: flex; flex-wrap: wrap; text-align: center;
	blockquote{width: 100%; display: block; font-size: 32px; @include font-one;}
	p{width: 100%; display: block;}
	i{display: inline-block; margin: 0 3px; color: $anarenk-koyu-kirmizi;}
	h6{width: 100%; display: block; margin: 0;}
	figure{width: 100%; display: block; margin: 0;
		img{height: 440px;}}}




/* CONTACT BOX */
.contact-box{width: 100%; display: flex; flex-wrap: wrap; margin: 0; padding: 0; @include font-one;
	li{width: 100%; display: flex; margin-bottom: 15px; padding: 0; list-style: none; line-height: 1.2;
		&:last-child{margin-bottom: 0;}
		h6{width: 100px; display: inline-block; color: $anarenk-koyu-kirmizi; margin: 0;}
		span{display: inline-block;}
		a{display: inline-block; text-decoration: underline;}}}





/* CONTACT FORM */
.contact-form{width: 100%; display: block; margin: 0; padding: 0;
	.form-group{width: 100%; display: block;
		&:last-child{margin-bottom: 0;}}}




/* GOOGLE MAPS */
.google-maps{width: 100%; display: block; position: relative;
	iframe{width: 100%; height: 500px; display: block; border:none; filter: grayscale(1); position: relative; z-index: 0;}
	.timetable{ width: 340px; display: flex; flex-wrap: wrap; position: absolute; left: 100px; top: 50%; transform: translateY(-50%); background: $anarenk-koyu-kirmizi; padding: 40px; margin: 0; z-index: 1;
		li{ width: 100%; display: flex; flex-wrap: wrap; margin: 10px 0; padding: 0; list-style: none;
			span{ color: #fff;}
			b{font-weight: 400; margin-left: auto; color:$anarenk-koyu-kirmizi;}}}}


/* icon */
.icon {
	width: 90px;
	height: 93px;
	line-height: 80px;
	border-radius: 5px;
	color: $anarenk-koyu-kirmizi;
	font-size: 50px;
	-webkit-transition: .5s;
	transition: .5s;
	margin-left: auto;
	margin-right: auto;
	margin-bottom: 15px;
	font-size: 7rem;
}
.icon:hover {
	color: $siyah-renk;
}


// phone
.iconp {
    font-size: 3.5rem;
    float: left;
    margin-top: 0.5rem;
}

/*icon white*/
.iconw {
	width: 90px;
	height: 93px;
	line-height: 80px;
	border-radius: 5px;
	color: $beyaz-renk;
	font-size: 50px;
	-webkit-transition: .5s;
	transition: .5s;
	margin-left: auto;
	margin-right: auto;
	margin-bottom: 15px;
	font-size: 7rem;
}
.iconw:hover {
	color: rgb(235, 235, 235);
}

/* PAGINATION */
.pagination{ width: 100%; display: flex; flex-wrap: wrap; margin: 0;
	.page-item{display: inline-block;
		.page-link{ height: 60px; line-height: 60px; padding: 0 40px; border-radius: 0 !important; font-weight: 500; color: $anarenk-koyu-kirmizi; outline: none !important;
			&:focus{outline: none !important;}}}}






/* FOOTER */
.footer{width: 100%; flex-wrap: wrap; padding: 100px 0; background: $anarenk-koyu-kirmizi; position: relative; color: #fff;
	.logo{width: 100%; display: block; margin-bottom: 30px;
		img{width: auto; height: 50px;}}
	.footer-info{width: 100%; display: block; margin-bottom: 20px; @include font-one;
		a{color: #fff; text-decoration: underline; color: $anarenk-koyu-kirmizi; font-size: 14px;}}
	.copyright{width: 100%; display: block; margin: 0; font-size: 14px;}
	.footer-social{width: 100%; display: flex; flex-wrap: wrap;     margin-top: -37px; padding: 0;font-size: 2rem;
		li{display: inline-block; margin-right: 10px; padding: 0; list-style: none;
			a{ width: 40px; height: 40px; line-height: 40px; display: inline-block; border:1px solid rgba(255, 255, 255, 0.3); color: #fff; text-align: center; font-size: 13px;
				&:hover{background: $anarenk-koyu-kirmizi; border-color:transparent;}}}}
	.widget-title{width: 100%; display: block; font-weight: 500; font-size: 26px; margin-top: 10px; margin-bottom: 15px;}
	.footer-menu{width: 100%; display: flex; flex-wrap: wrap; margin: 0; padding: 0;
		li{width: 100%; display: flex; flex-wrap: wrap; align-items: center ; margin: 0; padding: 5px 0; list-style: none;
			&:before{content: ""; width: 4px; height: 4px; display: inline-block; background:$anarenk-koyu-kirmizi; border-radius: 50%; margin-right: 9px;}
			a{color: #fff;}}}}




/* RESPONSIVE MEDIUM  */
@media only screen and (max-width: 1199px), only screen and (max-device-width: 1199px) {
	.page-header {
		width: 100%;
		height: 926px;
		max-height: 201vh;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		position: relative;
		background-size: cover !important;
		padding-top: 1px;
	}
	.col-lg-4:nth-child(1) .image-box{padding-right: 0;}
	.col-lg-4:nth-child(3) .image-box{padding-left: 0;}
	.side-content h2 { font-size: 60px;}
	.side-content h2 br{display: none;}
	.counter-box{padding: 30px;}
	.side-content figure img{height: 70px;}
	.carousel-classes h6{font-size: 38px;}
	.class-box h6{font-size: 38px;}
	.sidebar{padding-left: 0;}
	.sidebar .widget{padding: 30px; margin-bottom: 30px;}
	.blog-box .content .full-width{width: 100%; margin-left: 0; margin-right: 0;}
	.recent-news .content{padding: 30px;}
	.footer .footer-menu li a{font-size: 17px;}
}



/* RESPONSIVE TABLET  */
@media only screen and (max-width: 991px), only screen and (max-device-width: 991px) {
	.page-header {
		width: 100%;
		height: 926px;
		max-height: 201vh;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		position: relative;
		background-size: cover !important;
		padding-top: 1px;
	}
	.side-widget .hide-mobile{display: none;}
	.side-widget .show-mobile{display: flex;}
	.side-widget .site-menu ul li{opacity: 1 !important;}
	.side-widget .site-menu ul li a{font-size: 22px;}
	.navbar .site-menu{display: none;}
	.slider .button-prev{display: none;}
	.slider .button-next{display: none;}
	.slider .main-slider .swiper-slide .container h1 {font-size: 60px;}
	.slider .main-slider .swiper-slide .container h1 br{display: none;}
	.col-lg-4:nth-child(2) .image-box{margin-top: 0; margin-bottom: 50px;}
	.no-spacing .side-content{padding: 100px 0 !important;}
	.side-image.full-right{width: 100%;}
	.side-image.full-left{width: 100%;}
	.side-image .side-timetable{width: 100%; position: static;}
	.col-lg-4:nth-child(3) .counter-box{margin-top: 50px;}
	.carousel-classes h6{font-size: 30px;}
	.content-section.bottom-dark-spacing:after{display: none;}
	.col-lg-6:nth-child(1) .pass-box{border-right: 0; margin-bottom: 50px;}
	.col-lg-4:nth-child(3) .recent-news{margin-top: 30px;}
	.side-gallery{width: calc(100% + 10px); margin-left: -5px; margin-right: -5px;}
	.side-gallery figure{margin: 5px !important;}
	.col-lg-3:nth-child(1) .branch-box{margin-bottom: 50px;}
	.col-lg-3:nth-child(2) .branch-box{margin-bottom: 50px;}
	.col-lg-4:nth-child(4) .member-box{margin-top: 30px;}
	.tab-wrapper .tab-nav{width: 100%; display: flex; padding: 0; justify-content: space-between}
	.tab-wrapper .tab-nav li{width: auto; flex: 1; display: inline-block;}
	.tab-wrapper .tab-nav li.active a{width: 100%; margin-right: 0;}
	.tab-wrapper .tab-item{width: 100%;}
	.tab-wrapper .tab-item .tab-inner ul{height: 50vw;}
	.section-title h2{font-size: 54px;}
	.section-title h2 br{display: none;}
	.side-content.left{padding-right: 0;}
	.all-classes{margin-bottom: 0;}
	.all-classes li{width: 50%;}
	.all-classes li:nth-child(3n+2){ transform: none;}
	.all-classes li:nth-child(3){margin-top: 50px;}
	.sidebar{margin-top: 50px;}
	.sidebar .widget .side-gallery li{width: 33.33333%;}
	.contact-box{margin-bottom: 50px;}
	.footer .copyright{margin-top: 40px;}
	.footer .widget-title{margin-top: 50px;}
}


/* RESPONSIVE MOBILE */
@media only screen and (max-width: 767px), only screen and (max-device-width: 767px) {
	.side-widget{max-width: 80vw;}
	.topbar div b{display: none;}
	.topbar div{font-size: 14px;}
	.navbar .navbar-button{display: none;}
	.slider .main-slider .swiper-slide{padding: 0; padding-top: 100px;}
	.page-header {
		width: 100%;
		height: 926px;
		max-height: 201vh;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		position: relative;
		background-size: cover !important;
		padding-top: 1px;
	}
	.footer .footer-social {
		width: 100%;
		padding: 0;
		text-align: center;
		align-items: center;
		clear: both;
		margin: auto;
		font-size: 5rem;
		width: 100%;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
	}
	.iconw {
		width: 90px;
		height: 93px;
		line-height: 80px;
		border-radius: 5px;
		color: #fff;
		font-size: 50px;
		-webkit-transition: .5s;
		transition: .5s;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 15px;
		font-size: 18rem;
		text-align: center;
		align-items: center;
		clear: both;
		margin: auto;
		padding: 0;
		text-align: center;
		align-items: center;
		clear: both;
		margin: auto;
		width: 100%;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
	}
	.icon {
		width: 90px;
		height: 93px;
		line-height: 80px;
		border-radius: 5px;
		color: #2e276a;
		font-size: 50px;
		-webkit-transition: .5s;
		transition: .5s;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 15px;
		font-size: 18rem;
		text-align: center;
		align-items: center;
		clear: both;
		margin: auto;
		padding: 0;
		text-align: center;
		align-items: center;
		clear: both;
		margin: auto;
		width: 100%;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
	}
	.page-header .container p {
		width: 100%;
		display: block;
		margin-bottom: 50px;
		font-size: 16px;
	}
	.slider .main-slider .swiper-slide .container h1{font-size: 40px;}
	.section-title h2{font-size: 42px;}
	.page-header .container{padding-top: 50px;}
	.page-header .container h2{font-size: 50px;font-size: 27px; line-height: 5rem;}
	.col-lg-4:nth-child(1) .image-box{margin-bottom: 50px;}
	.side-content h2{font-size: 42px;}
	.side-image .side-timetable{padding: 30px;}
	.col-lg-4:nth-child(2) .counter-box{margin-top: 30px;}
	.side-member figcaption h5{font-size: 40px;}
	.pass-box h6{font-size: 38px;}
	.pass-box p{padding: 0;}
	.video a{transform: scale(0.7);}
	.video a:hover{transform: scale(0.8);}
	.pagination .page-item .page-link{padding: 0 30px;}
	.google-maps iframe{display: flex;}
	.google-maps .timetable{width: 100%; position: static; padding: 30px; margin-bottom: -100px;}
	.blog-box .content h3{font-size: 44px;}
	.all-classes li:nth-child(2){margin-top: 50px;}
	.all-classes li{width: 100%;}
	.class-box h6{padding: 0; font-size: 32px;}
	.tab-wrapper .tab-nav{max-width: 100%; overflow-x: auto;}
	.tab-wrapper .tab-item .tab-inner{flex-wrap: wrap;}
	.tab-wrapper .tab-item .tab-inner ul{width: 100%; height: 300px; margin: 30px 0; padding: 30px;}
	.tab-wrapper .tab-item .tab-inner figure{width: 100%;}
	.cta-box{padding: 30px;}
	.cta-box h2{font-size: 44px;}
	.cta-box .custom-button{padding: 0; text-align: center; width: 100%;}
	.testimonial figure img{height: auto;}
	.col-lg-4:nth-child(3) .member-box{margin-top: 30px;}
	.col-lg-4:nth-child(2) .recent-news{margin-top: 30px;}
	.side-gallery{margin-top: 100px;}
	.side-gallery figure{width: calc(50% - 10px);}
	.col-lg-3:nth-child(3) .branch-box{margin-bottom: 50px;}
}

@media (min-width: 767px) and (max-width: 850px) {
	.footer .footer-social {
		width: 100%;
		padding: 0;
		text-align: center;
		align-items: center;
		clear: both;
		margin: auto;
		font-size: 5rem;
		width: 100%;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
	}
	.iconw {
		width: 90px;
		height: 93px;
		line-height: 80px;
		border-radius: 5px;
		color: #fff;
		font-size: 50px;
		-webkit-transition: .5s;
		transition: .5s;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 15px;
		font-size: 18rem;
		text-align: center;
		align-items: center;
		clear: both;
		margin: auto;
		padding: 0;
		text-align: center;
		align-items: center;
		clear: both;
		margin: auto;
		width: 100%;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
	}
	.icon {
		width: 90px;
		height: 93px;
		line-height: 80px;
		border-radius: 5px;
		color: #2e276a;
		font-size: 50px;
		-webkit-transition: .5s;
		transition: .5s;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 15px;
		font-size: 18rem;
		text-align: center;
		align-items: center;
		clear: both;
		margin: auto;
		padding: 0;
		text-align: center;
		align-items: center;
		clear: both;
		margin: auto;
		width: 100%;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
	}
	.baslik-3white{
		margin-top: 10rem;
	}
}

@media only screen and (min-width: 1180px) and (max-width: 1180px) {
	.side-widget .show-mobile {
		display: block;
	}
	.navbar .site-menu {
		margin: 0 auto;
		display: none;
	}
	.side-widget .hide-mobile {
		width: 100%;
		display: inline-block;
		display: none;
	}
  }
  .services-kutu2 {
    border-radius: 55px;
    border: 2px solid #fff;
}
.services-kutu2.tt {
    background: #f7f7f7;
    border-radius: 55px;
    border-bottom: 5px solid #2e276a;
}

$size : 120px;
$color1 : #2e276a;
$color2 : #FFFFFF;
@mixin keyframes($name) {
  @-o-keyframes #{$name} { @content };
  @-moz-keyframes #{$name} { @content };
  @-webkit-keyframes #{$name} { @content }; 
  @keyframes #{$name} { @content };
}
@mixin animation($prop...) {
  -o-animation: $prop;
  -moz-animation: $prop;
  -ms-animation: $prop;
  -webkit-animation: $prop;
  animation: $prop;
}
@mixin transform($prop...) {
  -o-transform: $prop;
  -moz-transform: $prop;
  -webkit-transform: $prop;
  -ms-transform: $prop;
  transform: $prop;
}
@mixin transform-origin($prop...) {
  -o-transform-origin: $prop;
  -moz-transform-origin: $prop;
  -webkit-transform-origin: $prop;
  -ms-transform-origin: $prop;
  transform-origin: $prop;
}


.loadwraps{
   width: 100%;
   height: 100vh;
   position: fixed;
   top: 0;
   left: 0;
   z-index: 99999;
   pointer-events: none;
   background: #fff;
   transition: all 1s;
   &:before,
   &:after,
   .dot,
   .outline{
      position: absolute;
      top: 50%;
      left: 50%;
      @include transform(translate(-50%,-50%));
      border-radius:50%;
   }

   .dot{
      width: #{$size - $size / 100 * 20};
      height: #{$size - $size / 100 * 20};
      background: $color2;
      @include animation(in 4s linear infinite);
      z-index:2;
   }
   &:before{
      content: "";
      width: 0px;
      height: 0px;
      background: $color1;
      @include animation(out1 4s linear infinite);
   }
   &:after{
      content: "";
      width: 0px;
      height: 0px;
      background: $color2;
      @include animation(out2 4s linear infinite);
   }
   .outline{
      width: $size;
      height: $size;
      z-index:2;
      span{
         width: $size / 2 + 8;
         height: $size / 2 + 8;
         @include transform-origin(100% 100%);
         @include transform(rotate(45deg) skewX(80deg));
         overflow: hidden;
         position: absolute;
         bottom: 50%; 
         right: 50%;
         @include animation(outline 4s linear infinite);
         &:before{
            content: "";
            display: block;
            border: solid 5px #fff;
            width: 200%;
            height: 200%;
            border-radius: 50%;
            @include transform(skewX(-80deg));
            @include animation(outlineBefore 4s linear infinite);
         }
      }
   }
}

@include keyframes(outline){
   0%{
      @include transform(rotate(0deg) skewX(80deg));
   }
   25%{
      @include transform(rotate(500deg) skewX(15deg));
   }
   50%{
      @include transform(rotate(1000deg) skewX(40deg));
   }
   75%{
      @include transform(rotate(1500deg) skewX(60deg));
   }
   100%{
      @include transform(rotate(2160deg) skewX(80deg));
   }
}

@include keyframes(outlineBefore){
   0%{
      @include transform(skewX(-80deg));
      border: solid 5px #ffffff;
   }
   25%{
      @include transform(skewX(-15deg));
      border: solid 5px #ffffff;
   }
   49%{
      border: solid 5px #ffffff;
   }
   50%{
      @include transform(skewX(-40deg));
      border: solid 5px #2e276a;
   }
   75%{
      @include transform(skewX(-60deg));
      border: solid 5px #2e276a;
   }
   100%{
      @include transform(skewX(-80deg));
      border: solid 5px #2e276a;
   }
}

@include keyframes(in){
   0%{
      width: #{$size + $size / 100 * 20};
      height: #{$size + $size / 100 * 20};
      background:$color2;
   }
   40%{
      width: 0px;
      height: 0px;
      background:$color2;
   }
   41%{
      width: 0px;
      height: 0px;
      background:$color1;
   }
   50%{
      width: #{$size + $size / 100 * 20};
      height: #{$size + $size / 100 * 20};
      background:$color1;
   }
   90%{
      width: 0px;
      height: 0px;
      background:$color1;
   }
   91%{
      width: 0px;
      height: 0px;
      background:$color2;
   }
   100%{
      width: #{$size + $size / 100 * 20};
      height: #{$size + $size / 100 * 20};
      background:$color2;
   }
}

@include keyframes(out1){
   0%{
      width:0px;
      height:0px;
   }
   30%{
      width:120vw;
      height:120vw;
   }
   100%{
      width:120vw;
      height:120vw;
   }
}

@include keyframes(out2){
   0%{
      width:0px;
      height:0px;
   }
   30%{
      width:0px;
      height:0px;
   }
   60%{
      width:120vw;
      height:120vw;
   }
   100%{
      width:120vw;
      height:120vw;
   }
}


.fade12 {
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none;
 }