/* -------------------------------------------------------
                  contact
-------------------------------------------------------- */
.box-info-contact {
  position: relative;
  background-color: $dark-color;
  height: 100%;
  padding: 30px;

  @include v-light {
    background-color: $bg-light-2;

    @include media_991 {
      background-color: transparent;
    }
  }

  @include media_768 {
    padding: 0;
    margin-bottom: 50px;
    background-color: transparent;
  }

  h3 {
    margin-bottom: 30px;

    @include media_768 {
      font-size: 25px;
    }
  }

  h5 {
    font-family: $heading-font;
    margin-bottom: 30px;
    line-height: 1.6;

    @include media_768 {
      margin-bottom: 10px;
      font-weight: 600;
    }
  }

  ul {
    li {
      margin-top: 30px;

      @include media_768 {
        margin-top: 10px;
      }

      span {
        color: #fff;
        font-weight: 600;
        font-size: 13px;
        text-transform: uppercase;
        min-width: 120px;

        @include v-light {
          color: $heading-color-light;
        }

        @include media_400 {
          min-width: auto;
          margin-right: 15px;
        }
      }

      a {
        position: relative;
        transition: color 0.3s ease-out;
        line-height: 1.7;

        &::before {
          content: "";
          position: absolute;
          height: 1px;
          width: 100%;
          bottom: 0;
          left: 0;
          background-color: #3535359c;
          transform-origin: 100% 50%;
          transition: transform 0.4s cubic-bezier(0.28, 0.38, 0, 0.81),
            -webkit-transform 0.4s cubic-bezier(0.28, 0.38, 0, 0.81);

          @include v-light {
            background-color: $border-color-light;
          }
        }

        &:after {
          content: "";
          position: absolute;
          height: 1px;
          width: 100%;
          bottom: 0;
          left: 0;
          background-color: #3535359c;
          transform: scaleX(0);
          transform-origin: 0 50%;
          transition: transform 0.4s cubic-bezier(0.28, 0.38, 0, 0.81) 0.2s,
            -webkit-transform 0.4s cubic-bezier(0.28, 0.38, 0, 0.81) 0.2s;

          @include v-light {
            background-color: $border-color-light;
          }
        }

        &:hover {
          &::before {
            transform: scaleX(0);
          }

          &:after {
            transform: scaleX(1);
          }
        }
      }
    }
  }
}

.form-box {
  position: relative;
  padding: 30px;

  @include media_768 {
    padding: 0;
    margin-bottom: 30px;
  }

  h3 {
    margin-bottom: 30px;

    @include media_768 {
      font-size: 25px;
    }

    @include media_400 {
    }
  }

  .form-group {
    width: 100%;

    .help-block {
      color: red;
      font-weight: 600;
      margin-bottom: 15px;
    }

    li {
      list-style: none;
    }
  }
}

.entry {
  display: flex;
  width: 100%;
  border-bottom: 1px solid #3535359c;
  padding-bottom: 15px;
  margin-bottom: 30px;

  @include v-light {
    border-bottom: 1px solid $border-color-light;
  }

  @include media_768 {
    display: block;
  }
}

.entry input {
  height: 100%;
}

label {
  flex: 0 0 auto;
  padding-right: 30px;
  color: #fff;
  font-size: 15px;
  letter-spacing: 1px;

  @include v-light {
    color: $heading-color-light;
  }

  @include media_768 {
    flex: unset;
    display: block;
    margin-bottom: 10px;
  }
}

#map {
  .gm-fullscreen-control,
  .gm-bundled-control,
  .gm-bundled-control-on-bottom,
  .gmnoprint,
  .gm-style-cc {
    display: none !important;
  }
}

.map-custom {
  position: relative;
  width: 100%;
  height: 80vh;
  pointer-events: none;
}
